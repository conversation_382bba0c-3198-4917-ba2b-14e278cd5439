<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终 LaTeX 公式渲染测试</title>
    
    <!-- 加载MathJax -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.9/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
    <script type="text/x-mathjax-config">
    MathJax.Hub.Config({
        tex2jax: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true
        },
        "HTML-CSS": { 
            fonts: ["TeX"],
            scale: 100,
            linebreaks: { automatic: true },
            availableFonts: ["TeX"],
            preferredFont: "TeX",
            webFont: "TeX",
            imageFont: null,
            undefinedFamily: "STIXGeneral,'Arial Unicode MS',serif",
            mtextFontInherit: false,
            EqnChunk: 50,
            EqnChunkFactor: 1.5,
            EqnChunkDelay: 100,
            matchFontHeight: true,
            noReflows: true,
            styles: {
                ".MathJax_Display": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                }
            }
        },
        messageStyle: "none",
        showProcessingMessages: false,
        showMathMenu: false,
        showMathMenuMSIE: false
    });
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .original-text {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85em;
            white-space: pre-wrap;
            border: 1px solid #ffeaa7;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .rendered-content {
            background: #d4edda;
            padding: 20px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }
        
        /* MathJax数学公式样式 */
        .math-display {
            display: block;
            margin: 1.5em 0;
            text-align: center;
            overflow-x: auto;
            max-width: 100%;
            padding: 1em 0;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .math-inline {
            display: inline-block;
            vertical-align: middle;
            margin: 0 0.2em;
            padding: 0 0.3em;
        }

        /* 确保MathJax生成的元素可见 */
        .MathJax {
            display: inline-block !important;
            overflow: visible !important;
            max-width: 100%;
        }

        .MathJax_Display {
            overflow: visible !important;
            margin: 1.5em 0;
            padding: 1em 0;
            max-width: 100%;
            text-align: center;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
        }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .rendered-content h3 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        .rendered-content h4 {
            color: #34495e;
            margin-top: 1.2em;
            margin-bottom: 0.4em;
        }

        .rendered-content p {
            margin-bottom: 1em;
        }

        .rendered-content strong {
            font-weight: 600;
            color: #2c3e50;
        }

        .rendered-content ul, .rendered-content ol {
            margin-left: 20px;
            margin-bottom: 1em;
        }

        .rendered-content li {
            margin-bottom: 0.5em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 最终 LaTeX 公式渲染测试</h1>
        
        <div class="status info" id="status">正在初始化 MathJax...</div>
        
        <div class="test-section">
            <h3>📝 用户提供的完整测试文本</h3>
            <div class="original-text" id="original-text">正在加载测试文本...</div>
            <div class="rendered-content" id="rendered-content">正在渲染...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // 用户提供的完整测试文本（包含问题公式）
        const userTestText = `### 详细解析

本题涉及一个边长为 \\(a\\) 的正方形平面，$$a^2+\\frac{a}{2}$$, 在其垂直轴线上距离中心点 \\(O\\) 为 \\(a/2\\) 处放置一个点电荷 \\(q\\)（正电荷），要求计算通过该平面的电场强度通量。原始解析的核心观点是正确的：基于高斯定律和对称性分析，通过该平面的通量为 \\(\\frac{q}{6\\epsilon_0}\\)（对应选项 A）。下面我将详细展开这一结论，融入相关物理概念和原理，确保解析清晰易懂。

#### 1. **核心概念回顾：高斯定律**
   - **高斯定律**是电磁学的基本原理之一，它描述了电场与电荷分布的关系。定律指出：通过任意封闭曲面（高斯面）的电场强度通量 \\(\\Phi_E\\) 等于该曲面内包围的净电荷 \\(q_{\\text{enc}}\\) 除以真空介电常数 \\(\\epsilon_0\\)：
     \\[
     \\Phi_E = \\oint \\mathbf{E} \\cdot d\\mathbf{A} = \\frac{q_{\\text{enc}}}{\\epsilon_0}
     \\]
     其中，\\(\\mathbf{E}\\) 是电场强度矢量，\\(d\\mathbf{A}\\) 是曲面面积微元的矢量（方向垂直于曲面），积分表示对整个封闭曲面的通量求和。
   - **点电荷的特殊性**：对于一个孤立的点电荷 \\(q\\)，其电场是球对称的（电场强度大小为 \\(E = \\frac{1}{4\\pi\\epsilon_0} \\frac{q}{r^2}\\)，方向沿径向）。如果点电荷位于封闭曲面内，则通过该封闭曲面的总通量恒为 \\(\\frac{q}{\\epsilon_0}\\)，与曲面的形状和大小无关（前提是电荷在曲面内）。这源于电场线的连续性：点电荷发出的电场线均匀发散，总通量由电荷量决定。

#### 2. **开曲面的处理：构造封闭曲面**
   - 本题中的正方形平面是一个**开曲面**（非封闭），因此不能直接应用高斯定律计算通量。为了求解，需要构造一个合适的封闭曲面，利用对称性间接推导。
   - **构造立方体高斯面**：以点电荷为中心，构造一个边长为 \\(a\\) 的立方体（如图示意）。点电荷位于立方体一个面的中心，因为题目给定点电荷在正方形平面中垂线上距中心 \\(O\\) 点 \\(a/2\\) 处，而立方体面中心到其几何中心的距离恰好为 \\(a/2\\)（立方体边长 \\(a\\)，几何中心到面中心的距离为半边长，即 \\(a/2\\)）。
     - 为什么选择立方体？点电荷的电场具有径向对称性，而立方体具有高度对称性（各面几何形状相同），这确保了电场线在立方体各面上均匀分布。
   - **封闭曲面的通量**：点电荷位于立方体内部，因此通过整个立方体的总通量为：
     \\[
     \\Phi_{\\text{total}} = \\frac{q}{\\epsilon_0}
     \\]

#### 3. **对称性分析与通量分配**
   - **对称性原理**：点电荷位于立方体几何中心，电场线呈放射状均匀分布。立方体有 6 个完全相同的面（每个面为正方形，边长 \\(a\\)），因此电场线等量地穿过每个面。这意味着通过每个面的通量相等。
   - **计算单个面的通量**：设通过每个面的通量为 \\(\\Phi_{\\text{face}}\\)，则总通量可分解为：
     \\[
     \\Phi_{\\text{total}} = 6 \\Phi_{\\text{face}}
     \\]
     代入总通量公式：
     \\[
     6 \\Phi_{\\text{face}} = \\frac{q}{\\epsilon_0} \\implies \\Phi_{\\text{face}} = \\frac{q}{6\\epsilon_0}
     \\]
     因此，通过原始正方形平面（即立方体中的一个面）的通量即为 \\(\\frac{q}{6\\epsilon_0}\\)。

#### 4. **结论与答案验证**
   - 通过该平面的电场强度通量为 \\(\\frac{q}{6\\epsilon_0}\\)，选项 A 正确。
   - **错误选项分析**：
     - **选项 B**：可能是点电荷的电场强度公式 \\(E = \\frac{1}{4\\pi\\epsilon_0} \\frac{q}{r^2}\\)，但问题要求的是通量（标量，单位 N·m²/C），而非电场强度（矢量）。混淆两者会导致错误。
     - **选项 C 和 D**：如 \\(\\frac{q}{12\\epsilon_0}\\) 或类似值，缺乏物理依据。可能源于错误假设（如误用距离或不对称构造），但本题的对称性确保了 \\(\\frac{q}{6\\epsilon_0}\\) 是唯一合理结果。

#### 5. **附加说明：通量计算的通用方法**
   - 如果对称性不足，可直接积分求解（通量定义为 \\(\\Phi_E = \\int \\mathbf{E} \\cdot d\\mathbf{A}\\))，但本题的点电荷位置和平面几何使积分复杂（需考虑电场强度的空间变化和角度）。对称性方法更简洁高效，体现了高斯定律的威力。
   - **物理意义**：通量 \\(\\frac{q}{6\\epsilon_0}\\) 表示穿过平面的电场线比例（约 16.7%）。如果点电荷位置改变（如不在面中心），对称性破坏，通量会不同，但本题条件确保了解答的准确性。

本解析强化了原始解析的核心逻辑，同时融入高斯定律、对称性和电场分布原理，帮助深入理解问题。最终答案：\\(\\boxed{\\dfrac{q}{6\\epsilon_0}}\\)（选项 A）。`;

        // 专门测试问题公式
        const problemFormulaText = `测试问题公式：

\\[
     6 \\Phi_{\\text{face}} = \\frac{q}{\\epsilon_0} \\implies \\Phi_{\\text{face}} = \\frac{q}{6\\epsilon_0}
     \\]

这个公式应该能正确渲染为行间公式。`;

        // 基于 MathJax 的数学公式处理函数（复制自 gen_questions.html）
        function preprocessLatexFormula(formula) {
            let processed = formula.replace(/\\\\/g, '___SINGLE_BACKSLASH___');
            processed = processed.replace(/\\([^a-zA-Z0-9])/g, '___ESCAPED_$1___');
            return processed;
        }
        
        function restoreLatexFormula(formula) {
            let restored = formula.replace(/___SINGLE_BACKSLASH___/g, '\\');
            restored = restored.replace(/___ESCAPED_([^a-zA-Z0-9])___/g, '\\$1');
            return restored;
        }

        function processMathFormulas(content) {
            try {
                let processedContent = content;
                const mathPlaceholders = [];
                let mathCounter = 0;
                
                // 处理块级公式 $$ ... $$
                processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, function(match, formula) {
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理行内公式 \( ... \)
                processedContent = processedContent.replace(/\\\(([\s\S]*?)\\\)/g, function(match, formula) {
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_INLINE_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'inline',
                        delimiter: '\\(',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理行内公式 $ ... $
                processedContent = processedContent.replace(/\$([^$\n]+?)\$/g, function(match, formula) {
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_INLINE_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'inline',
                        delimiter: '$',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                return { processedContent, mathPlaceholders };
            } catch (error) {
                console.error('Error processing math formulas:', error);
                return { processedContent: content, mathPlaceholders: [] };
            }
        }

        function renderMarkdown(content) {
            try {
                // 处理换行符转换
                let processedContent = content.replace(/\\n/g, '\n');
                
                const { processedContent: contentWithPlaceholders, mathPlaceholders } = processMathFormulas(processedContent);
                
                // 使用 marked 处理 Markdown
                let renderedContent = marked.parse(contentWithPlaceholders);
                
                // 恢复数学公式
                mathPlaceholders.forEach(item => {
                    const uniqueId = `math-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                    
                    let formula = item.formula;
                    if (item.needsRestore) {
                        formula = restoreLatexFormula(formula);
                    }
                    
                    if (item.type === 'block') {
                        renderedContent = renderedContent.replace(
                            item.placeholder, 
                            `<div class="math-display" id="${uniqueId}">\\[${formula}\\]</div>`
                        );
                    } else {
                        renderedContent = renderedContent.replace(
                            item.placeholder, 
                            `<span class="math-inline" id="${uniqueId}">\\(${formula}\\)</span>`
                        );
                    }
                });
                
                return renderedContent;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return `<div class="error">渲染错误: ${error.message}</div>`;
            }
        }

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', function() {
            // 显示原始文本
            document.getElementById('original-text').textContent = userTestText;
            
            // 等待 MathJax 加载
            function checkMathJaxReady() {
                if (typeof MathJax === 'undefined' || !MathJax.Hub) {
                    setTimeout(checkMathJaxReady, 300);
                    return;
                }
                
                document.getElementById('status').textContent = 'MathJax 已加载，开始渲染...';
                document.getElementById('status').className = 'status success';
                
                console.log('MathJax is ready, rendering test content');

                // 渲染用户测试文本
                const rendered = renderMarkdown(userTestText);
                document.getElementById('rendered-content').innerHTML = rendered;

                // 添加问题公式测试
                const problemRendered = renderMarkdown(problemFormulaText);
                const problemSection = document.createElement('div');
                problemSection.className = 'test-section';
                problemSection.innerHTML = `
                    <h3>🔧 专门测试问题公式</h3>
                    <div class="original-text">${problemFormulaText.replace(/\n/g, '\\n')}</div>
                    <div class="rendered-content">${problemRendered}</div>
                `;
                document.querySelector('.container').appendChild(problemSection);

                // 触发 MathJax 渲染
                setTimeout(() => {
                    if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);

                        // 渲染完成后更新状态
                        setTimeout(() => {
                            document.getElementById('status').textContent = '✅ 渲染完成！请检查下方的公式是否正确显示。特别注意 \\[...\\] 格式的公式。';
                        }, 1000);
                    }
                }, 100);
            }
            
            checkMathJaxReady();
        });
    </script>
</body>
</html>
