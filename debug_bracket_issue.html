<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试 \\[...\\] 格式问题</title>
    
    <!-- 加载MathJax -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.9/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
    <script type="text/x-mathjax-config">
    MathJax.Hub.Config({
        tex2jax: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true
        },
        "HTML-CSS": { 
            fonts: ["TeX"],
            scale: 100,
            linebreaks: { automatic: true }
        },
        messageStyle: "none",
        showProcessingMessages: false,
        showMathMenu: false,
        showMathMenuMSIE: false
    });
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-case {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .original {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
        
        .processed {
            background: #d1ecf1;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
        
        .rendered {
            background: #d4edda;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .debug {
            background: #e2e3e5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.8em;
            white-space: pre-wrap;
        }
        
        .MathJax_Display {
            overflow: visible !important;
            margin: 1.5em 0;
            padding: 1em 0;
            max-width: 100%;
            text-align: center;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🔧 调试 \\[...\\] 格式问题</h1>
    
    <div class="test-case">
        <h3>测试用例 1: 用户原始问题文本</h3>
        <div class="original" id="original1"></div>
        <div class="processed" id="processed1"></div>
        <div class="debug" id="debug1"></div>
        <div class="rendered" id="rendered1"></div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 2: 简化版本</h3>
        <div class="original" id="original2"></div>
        <div class="processed" id="processed2"></div>
        <div class="debug" id="debug2"></div>
        <div class="rendered" id="rendered2"></div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 3: 直接 MathJax 测试</h3>
        <div class="original">直接使用 MathJax 语法：</div>
        <div class="rendered">
            \[
            6 \Phi_{\text{face}} = \frac{q}{\epsilon_0} \implies \Phi_{\text{face}} = \frac{q}{6\epsilon_0}
            \]
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // 复制 gen_questions.html 中的处理函数
        function preprocessLatexFormula(formula) {
            let processed = formula.replace(/\\\\/g, '___SINGLE_BACKSLASH___');
            processed = processed.replace(/\\([^a-zA-Z0-9])/g, '___ESCAPED_$1___');
            return processed;
        }
        
        function restoreLatexFormula(formula) {
            let restored = formula.replace(/___SINGLE_BACKSLASH___/g, '\\');
            restored = restored.replace(/___ESCAPED_([^a-zA-Z0-9])___/g, '\\$1');
            return restored;
        }

        function processMathFormulas(content) {
            try {
                let processedContent = content;
                const mathPlaceholders = [];
                let mathCounter = 0;
                
                console.log('=== processMathFormulas 开始 ===');
                console.log('原始内容:', JSON.stringify(content));
                
                // 处理块级公式 \\[ ... \\] (包含换行符的情况)
                const regex1 = /\\\\\[\s*\n([\s\S]*?)\n\s*\\\\\]/g;
                let match1;
                while ((match1 = regex1.exec(processedContent)) !== null) {
                    console.log('匹配到 \\\\[...\\\\] 格式 (换行):', match1[0]);
                    console.log('公式内容:', match1[1]);
                }
                
                processedContent = processedContent.replace(/\\\\\[\s*\n([\s\S]*?)\n\s*\\\\\]/g, function(match, formula) {
                    console.log('处理 \\\\[...\\\\] 格式 (换行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理块级公式 \\[ ... \\] (单行情况)
                processedContent = processedContent.replace(/\\\\\[([\s\S]*?)\\\\\]/g, function(match, formula) {
                    console.log('处理 \\\\[...\\\\] 格式 (单行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理块级公式 \[ ... \] (包含换行符的情况)
                processedContent = processedContent.replace(/\\\[\s*\n([\s\S]*?)\n\s*\\\]/g, function(match, formula) {
                    console.log('处理 \\[...\\] 格式 (换行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理块级公式 \[ ... \] (单行情况)
                processedContent = processedContent.replace(/\\\[([\s\S]*?)\\\]/g, function(match, formula) {
                    console.log('处理 \\[...\\] 格式 (单行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                console.log('处理后内容:', JSON.stringify(processedContent));
                console.log('数学占位符:', mathPlaceholders);
                console.log('=== processMathFormulas 结束 ===');
                
                return { processedContent, mathPlaceholders };
            } catch (error) {
                console.error('Error processing math formulas:', error);
                return { processedContent: content, mathPlaceholders: [] };
            }
        }

        function renderMarkdown(content) {
            try {
                // 处理换行符转换
                let processedContent = content.replace(/\\n/g, '\n');
                
                const { processedContent: contentWithPlaceholders, mathPlaceholders } = processMathFormulas(processedContent);
                
                // 使用 marked 处理 Markdown
                let renderedContent = marked.parse(contentWithPlaceholders);
                
                // 恢复数学公式
                mathPlaceholders.forEach(item => {
                    const uniqueId = `math-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                    
                    let formula = item.formula;
                    if (item.needsRestore) {
                        formula = restoreLatexFormula(formula);
                    }
                    
                    console.log('恢复公式:', formula);
                    
                    if (item.type === 'block') {
                        renderedContent = renderedContent.replace(
                            item.placeholder, 
                            `<div class="math-display" id="${uniqueId}">\\[${formula}\\]</div>`
                        );
                    } else {
                        renderedContent = renderedContent.replace(
                            item.placeholder, 
                            `<span class="math-inline" id="${uniqueId}">\\(${formula}\\)</span>`
                        );
                    }
                });
                
                return renderedContent;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return `<div class="error">渲染错误: ${error.message}</div>`;
            }
        }

        // 测试用例
        const testCase1 = "\\\\[\\n     6 \\\\Phi_{\\\\text{face}} = \\\\frac{q}{\\\\epsilon_0} \\\\implies \\\\Phi_{\\\\text{face}} = \\\\frac{q}{6\\\\epsilon_0}\\n     \\\\]";
        const testCase2 = "简化测试：\\\\[6 \\\\Phi = \\\\frac{q}{\\\\epsilon_0}\\\\]";

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', function() {
            // 等待 MathJax 加载
            function checkMathJaxReady() {
                if (typeof MathJax === 'undefined' || !MathJax.Hub) {
                    setTimeout(checkMathJaxReady, 300);
                    return;
                }
                
                console.log('MathJax is ready, running debug tests');
                
                // 测试用例 1
                document.getElementById('original1').textContent = testCase1;
                
                const processed1 = testCase1.replace(/\\n/g, '\n');
                document.getElementById('processed1').textContent = processed1;
                
                const { processedContent: content1, mathPlaceholders: placeholders1 } = processMathFormulas(processed1);
                document.getElementById('debug1').textContent = `处理后内容: ${JSON.stringify(content1)}\n占位符: ${JSON.stringify(placeholders1, null, 2)}`;
                
                const rendered1 = renderMarkdown(testCase1);
                document.getElementById('rendered1').innerHTML = rendered1;
                
                // 测试用例 2
                document.getElementById('original2').textContent = testCase2;
                
                const processed2 = testCase2.replace(/\\n/g, '\n');
                document.getElementById('processed2').textContent = processed2;
                
                const { processedContent: content2, mathPlaceholders: placeholders2 } = processMathFormulas(processed2);
                document.getElementById('debug2').textContent = `处理后内容: ${JSON.stringify(content2)}\n占位符: ${JSON.stringify(placeholders2, null, 2)}`;
                
                const rendered2 = renderMarkdown(testCase2);
                document.getElementById('rendered2').innerHTML = rendered2;
                
                // 触发 MathJax 渲染
                setTimeout(() => {
                    if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
                    }
                }, 100);
            }
            
            checkMathJaxReady();
        });
    </script>
</body>
</html>
