<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MathJax LaTeX 公式渲染测试</title>
    
    <!-- 加载MathJax -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.9/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
    <script type="text/x-mathjax-config">
    MathJax.Hub.Config({
        tex2jax: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true
        },
        "HTML-CSS": { 
            fonts: ["TeX"],
            scale: 100,
            linebreaks: { automatic: true },
            availableFonts: ["TeX"],
            preferredFont: "TeX",
            webFont: "TeX",
            imageFont: null,
            undefinedFamily: "STIXGeneral,'Arial Unicode MS',serif",
            mtextFontInherit: false,
            EqnChunk: 50,
            EqnChunkFactor: 1.5,
            EqnChunkDelay: 100,
            matchFontHeight: true,
            noReflows: true,
            styles: {
                ".MathJax_Display": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                }
            }
        },
        messageStyle: "none",
        showProcessingMessages: false,
        showMathMenu: false,
        showMathMenuMSIE: false
    });
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-case {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .original {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
        
        .rendered {
            background: #d4edda;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        /* MathJax数学公式样式 */
        .math-display {
            display: block;
            margin: 1.5em 0;
            text-align: center;
            overflow-x: auto;
            max-width: 100%;
            padding: 1em 0;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .math-inline {
            display: inline-block;
            vertical-align: middle;
            margin: 0 0.2em;
            padding: 0 0.3em;
        }

        /* 确保MathJax生成的元素可见 */
        .MathJax {
            display: inline-block !important;
            overflow: visible !important;
            max-width: 100%;
        }

        .MathJax_Display {
            overflow: visible !important;
            margin: 1.5em 0;
            padding: 1em 0;
            max-width: 100%;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>MathJax LaTeX 公式渲染测试</h1>
    
    <div class="test-case">
        <h3>测试用例 1: 简单行间公式</h3>
        <div class="original">原始内容：$$a^2+\\frac{a}{2}$$</div>
        <div class="rendered" id="test1">$$a^2+\frac{a}{2}$$</div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 2: 行内公式</h3>
        <div class="original">原始内容：本题涉及一个边长为 \\(a\\) 的正方形平面</div>
        <div class="rendered" id="test2">本题涉及一个边长为 \(a\) 的正方形平面</div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 3: 复杂公式</h3>
        <div class="original">原始内容：\\[\\Phi_E = \\oint \\mathbf{E} \\cdot d\\mathbf{A} = \\frac{q_{\\text{enc}}}{\\epsilon_0}\\]</div>
        <div class="rendered" id="test3">\[\Phi_E = \oint \mathbf{E} \cdot d\mathbf{A} = \frac{q_{\text{enc}}}{\epsilon_0}\]</div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 4: 用户提供的完整示例</h3>
        <div class="original">用户测试文本（部分）：
### 详细解析

本题涉及一个边长为 \(a\) 的正方形平面，$$a^2+\frac{a}{2}$$, 在其垂直轴线上距离中心点 \(O\) 为 \(a/2\) 处放置一个点电荷 \(q\)（正电荷）

通过该平面的电场强度通量为 \(\frac{q}{6\epsilon_0}\)，选项 A 正确。

\[
\Phi_{\text{total}} = 6 \Phi_{\text{face}}
\]

代入总通量公式：
\[
6 \Phi_{\text{face}} = \frac{q}{\epsilon_0} \implies \Phi_{\text{face}} = \frac{q}{6\epsilon_0}
\]</div>
        <div class="rendered" id="test4"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // 基于 MathJax 的数学公式处理函数
        function preprocessLatexFormula(formula) {
            // 新规则：将双反斜杠转换为单反斜杠
            let processed = formula.replace(/\\\\/g, '___SINGLE_BACKSLASH___');
            processed = processed.replace(/\\([^a-zA-Z0-9])/g, '___ESCAPED_$1___');
            return processed;
        }
        
        function restoreLatexFormula(formula) {
            let restored = formula.replace(/___SINGLE_BACKSLASH___/g, '\\');
            restored = restored.replace(/___ESCAPED_([^a-zA-Z0-9])___/g, '\\$1');
            return restored;
        }

        function processMathFormulas(content) {
            try {
                let processedContent = content;
                const mathPlaceholders = [];
                let mathCounter = 0;
                
                // 处理块级公式 $$ ... $$
                processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, function(match, formula) {
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理行内公式 \( ... \)
                processedContent = processedContent.replace(/\\\(([\s\S]*?)\\\)/g, function(match, formula) {
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_INLINE_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'inline',
                        delimiter: '\\(',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理行内公式 $ ... $
                processedContent = processedContent.replace(/\$([^$\n]+?)\$/g, function(match, formula) {
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_INLINE_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'inline',
                        delimiter: '$',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                return { processedContent, mathPlaceholders };
            } catch (error) {
                console.error('Error processing math formulas:', error);
                return { processedContent: content, mathPlaceholders: [] };
            }
        }

        function renderMarkdown(content) {
            try {
                // 处理换行符转换
                let processedContent = content.replace(/\\n/g, '\n');
                
                const { processedContent: contentWithPlaceholders, mathPlaceholders } = processMathFormulas(processedContent);
                
                // 使用 marked 处理 Markdown
                let renderedContent = marked.parse(contentWithPlaceholders);
                
                // 恢复数学公式
                mathPlaceholders.forEach(item => {
                    const uniqueId = `math-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                    
                    let formula = item.formula;
                    if (item.needsRestore) {
                        formula = restoreLatexFormula(formula);
                    }
                    
                    if (item.type === 'block') {
                        renderedContent = renderedContent.replace(
                            item.placeholder, 
                            `<div class="math-display" id="${uniqueId}">\\[${formula}\\]</div>`
                        );
                    } else {
                        renderedContent = renderedContent.replace(
                            item.placeholder, 
                            `<span class="math-inline" id="${uniqueId}">\\(${formula}\\)</span>`
                        );
                    }
                });
                
                return renderedContent;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return `<div class="error">渲染错误: ${error.message}</div>`;
            }
        }

        // 用户提供的测试文本
        const userTestText = `### 详细解析

本题涉及一个边长为 \\(a\\) 的正方形平面，$$a^2+\\frac{a}{2}$$, 在其垂直轴线上距离中心点 \\(O\\) 为 \\(a/2\\) 处放置一个点电荷 \\(q\\)（正电荷）

通过该平面的电场强度通量为 \\(\\frac{q}{6\\epsilon_0}\\)，选项 A 正确。

\\[
\\Phi_{\\text{total}} = 6 \\Phi_{\\text{face}}
\\]

代入总通量公式：
\\[
6 \\Phi_{\\text{face}} = \\frac{q}{\\epsilon_0} \\implies \\Phi_{\\text{face}} = \\frac{q}{6\\epsilon_0}
\\]`;

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', function() {
            // 等待 MathJax 加载
            function checkMathJaxReady() {
                if (typeof MathJax === 'undefined' || !MathJax.Hub) {
                    setTimeout(checkMathJaxReady, 300);
                    return;
                }
                
                console.log('MathJax is ready, rendering test content');
                
                // 渲染用户测试文本
                const rendered = renderMarkdown(userTestText);
                document.getElementById('test4').innerHTML = rendered;
                
                // 触发 MathJax 渲染
                setTimeout(() => {
                    if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
                    }
                }, 100);
            }
            
            checkMathJaxReady();
        });
    </script>
</body>
</html>
