<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>\\[...\\] 格式公式测试</title>
    
    <!-- 加载MathJax -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.9/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
    <script type="text/x-mathjax-config">
    MathJax.Hub.Config({
        tex2jax: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true
        },
        "HTML-CSS": { 
            fonts: ["TeX"],
            scale: 100,
            linebreaks: { automatic: true },
            availableFonts: ["TeX"],
            preferredFont: "TeX",
            webFont: "TeX",
            imageFont: null,
            undefinedFamily: "STIXGeneral,'Arial Unicode MS',serif",
            mtextFontInherit: false,
            EqnChunk: 50,
            EqnChunkFactor: 1.5,
            EqnChunkDelay: 100,
            matchFontHeight: true,
            noReflows: true,
            styles: {
                ".MathJax_Display": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                },
                ".MathJax": {
                    "overflow": "visible !important",
                    "max-width": "100%"
                }
            }
        },
        messageStyle: "none",
        showProcessingMessages: false,
        showMathMenu: false,
        showMathMenuMSIE: false
    });
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .original-text {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85em;
            white-space: pre-wrap;
            border: 1px solid #ffeaa7;
        }
        
        .rendered-content {
            background: #d4edda;
            padding: 20px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }
        
        /* MathJax数学公式样式 */
        .math-display {
            display: block;
            margin: 1.5em 0;
            text-align: center;
            overflow-x: auto;
            max-width: 100%;
            padding: 1em 0;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .math-inline {
            display: inline-block;
            vertical-align: middle;
            margin: 0 0.2em;
            padding: 0 0.3em;
        }

        /* 确保MathJax生成的元素可见 */
        .MathJax {
            display: inline-block !important;
            overflow: visible !important;
            max-width: 100%;
        }

        .MathJax_Display {
            overflow: visible !important;
            margin: 1.5em 0;
            padding: 1em 0;
            max-width: 100%;
            text-align: center;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
        }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .debug-info {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.9em;
            border: 1px solid #d6d8db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 \\[...\\] 格式公式测试</h1>
        
        <div class="status info" id="status">正在初始化 MathJax...</div>
        
        <div class="test-section">
            <h3>📝 测试用例 1: 简单 \\[...\\] 格式</h3>
            <div class="original-text">\\[
     6 \\Phi_{\\text{face}} = \\frac{q}{\\epsilon_0} \\implies \\Phi_{\\text{face}} = \\frac{q}{6\\epsilon_0}
     \\]</div>
            <div class="rendered-content" id="test1"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试用例 2: 带换行符的 \\[...\\] 格式</h3>
            <div class="original-text">\\[\\n     6 \\Phi_{\\text{face}} = \\frac{q}{\\epsilon_0} \\implies \\Phi_{\\text{face}} = \\frac{q}{6\\epsilon_0}\\n     \\]</div>
            <div class="rendered-content" id="test2"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试用例 3: 双反斜杠 \\\\[...\\\\] 格式</h3>
            <div class="original-text">\\\\[\\n     6 \\\\Phi_{\\\\text{face}} = \\\\frac{q}{\\\\epsilon_0} \\\\implies \\\\Phi_{\\\\text{face}} = \\\\frac{q}{6\\\\epsilon_0}\\n     \\\\]</div>
            <div class="rendered-content" id="test3"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试用例 4: 用户原始问题文本</h3>
            <div class="original-text">用户原始文本中的问题公式</div>
            <div class="rendered-content" id="test4"></div>
        </div>
        
        <div class="debug-info" id="debug-info">
            调试信息将在这里显示...
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // 复制 gen_questions.html 中的处理函数
        function preprocessLatexFormula(formula) {
            let processed = formula.replace(/\\\\/g, '___SINGLE_BACKSLASH___');
            processed = processed.replace(/\\([^a-zA-Z0-9])/g, '___ESCAPED_$1___');
            return processed;
        }
        
        function restoreLatexFormula(formula) {
            let restored = formula.replace(/___SINGLE_BACKSLASH___/g, '\\');
            restored = restored.replace(/___ESCAPED_([^a-zA-Z0-9])___/g, '\\$1');
            return restored;
        }

        function processMathFormulas(content) {
            try {
                let processedContent = content;
                const mathPlaceholders = [];
                let mathCounter = 0;
                
                console.log('原始内容:', content);
                
                // 处理块级公式 \\[ ... \\] (包含换行符的情况)
                processedContent = processedContent.replace(/\\\\\[\s*\n([\s\S]*?)\n\s*\\\\\]/g, function(match, formula) {
                    console.log('匹配到 \\\\[...\\\\] 格式 (换行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理块级公式 \\[ ... \\] (单行情况)
                processedContent = processedContent.replace(/\\\\\[([\s\S]*?)\\\\\]/g, function(match, formula) {
                    console.log('匹配到 \\\\[...\\\\] 格式 (单行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理块级公式 \[ ... \] (包含换行符的情况)
                processedContent = processedContent.replace(/\\\[\s*\n([\s\S]*?)\n\s*\\\]/g, function(match, formula) {
                    console.log('匹配到 \\[...\\] 格式 (换行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                // 处理块级公式 \[ ... \] (单行情况)
                processedContent = processedContent.replace(/\\\[([\s\S]*?)\\\]/g, function(match, formula) {
                    console.log('匹配到 \\[...\\] 格式 (单行):', match);
                    const preprocessedFormula = preprocessLatexFormula(formula);
                    const placeholder = `MATH_BLOCK_${mathCounter}`;
                    mathPlaceholders.push({
                        placeholder: placeholder,
                        formula: preprocessedFormula.trim(),
                        type: 'block',
                        delimiter: '\\[',
                        needsRestore: true
                    });
                    mathCounter++;
                    return placeholder;
                });
                
                console.log('处理后内容:', processedContent);
                console.log('数学占位符:', mathPlaceholders);
                
                return { processedContent, mathPlaceholders };
            } catch (error) {
                console.error('Error processing math formulas:', error);
                return { processedContent: content, mathPlaceholders: [] };
            }
        }

        function renderMarkdown(content) {
            try {
                // 处理换行符转换
                let processedContent = content.replace(/\\n/g, '\n');
                
                const { processedContent: contentWithPlaceholders, mathPlaceholders } = processMathFormulas(processedContent);
                
                // 使用 marked 处理 Markdown
                let renderedContent = marked.parse(contentWithPlaceholders);
                
                // 恢复数学公式
                mathPlaceholders.forEach(item => {
                    const uniqueId = `math-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                    
                    let formula = item.formula;
                    if (item.needsRestore) {
                        formula = restoreLatexFormula(formula);
                    }
                    
                    console.log('恢复公式:', formula);
                    
                    if (item.type === 'block') {
                        renderedContent = renderedContent.replace(
                            item.placeholder, 
                            `<div class="math-display" id="${uniqueId}">\\[${formula}\\]</div>`
                        );
                    } else {
                        renderedContent = renderedContent.replace(
                            item.placeholder, 
                            `<span class="math-inline" id="${uniqueId}">\\(${formula}\\)</span>`
                        );
                    }
                });
                
                return renderedContent;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return `<div class="error">渲染错误: ${error.message}</div>`;
            }
        }

        // 测试用例
        const testCases = [
            "\\[\n     6 \\Phi_{\\text{face}} = \\frac{q}{\\epsilon_0} \\implies \\Phi_{\\text{face}} = \\frac{q}{6\\epsilon_0}\n     \\]",
            "\\[\\n     6 \\Phi_{\\text{face}} = \\frac{q}{\\epsilon_0} \\implies \\Phi_{\\text{face}} = \\frac{q}{6\\epsilon_0}\\n     \\]",
            "\\\\[\\n     6 \\\\Phi_{\\\\text{face}} = \\\\frac{q}{\\\\epsilon_0} \\\\implies \\\\Phi_{\\\\text{face}} = \\\\frac{q}{6\\\\epsilon_0}\\n     \\\\]",
            "### 详细解析\\n\\n本题涉及一个边长为 \\\\(a\\\\) 的正方形平面，$$a^2+\\\\frac{a}{2}$$, 在其垂直轴线上距离中心点 \\\\(O\\\\) 为 \\\\(a/2\\\\) 处放置一个点电荷 \\\\(q\\\\)（正电荷）\\n\\n\\\\[\\n     6 \\\\Phi_{\\\\text{face}} = \\\\frac{q}{\\\\epsilon_0} \\\\implies \\\\Phi_{\\\\text{face}} = \\\\frac{q}{6\\\\epsilon_0}\\n     \\\\]\\n\\n这个公式应该能正确渲染。"
        ];

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', function() {
            // 等待 MathJax 加载
            function checkMathJaxReady() {
                if (typeof MathJax === 'undefined' || !MathJax.Hub) {
                    setTimeout(checkMathJaxReady, 300);
                    return;
                }
                
                document.getElementById('status').textContent = 'MathJax 已加载，开始测试...';
                document.getElementById('status').className = 'status success';
                
                console.log('MathJax is ready, running tests');
                
                let debugInfo = '';
                
                testCases.forEach((testCase, index) => {
                    console.log(`\n=== 测试用例 ${index + 1} ===`);
                    console.log('原始:', testCase);
                    
                    const rendered = renderMarkdown(testCase);
                    console.log('渲染结果:', rendered);
                    
                    document.getElementById(`test${index + 1}`).innerHTML = rendered;
                    
                    debugInfo += `测试用例 ${index + 1}:\n原始: ${testCase}\n渲染: ${rendered}\n\n`;
                });
                
                document.getElementById('debug-info').textContent = debugInfo;
                
                // 触发 MathJax 渲染
                setTimeout(() => {
                    if (MathJax.Hub && typeof MathJax.Hub.Queue === 'function') {
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
                        
                        // 渲染完成后更新状态
                        setTimeout(() => {
                            document.getElementById('status').textContent = '✅ 测试完成！请检查各个公式是否正确显示。';
                        }, 1000);
                    }
                }, 100);
            }
            
            checkMathJaxReady();
        });
    </script>
</body>
</html>
