<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助教 - 主页</title>
    <!-- 添加favicon链接 -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('main.static', filename='favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('main.static', filename='favicon.ico') }}">
    <link rel="stylesheet" href="{{ url_for('main.static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .api-key-input {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
        }
        
        .api-key-input .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
        }
        
        .api-key-input input {
            width: 100%;
            padding: 8px 35px 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .toggle-password {
            position: absolute;
            right: 2px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            outline: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 24px;
            height: 24px;
        }
        
        .toggle-password:hover,
        .toggle-password:focus,
        .toggle-password:active {
            background: transparent;
            outline: none;
            box-shadow: none;
            -webkit-tap-highlight-color: transparent;
        }
        
        .eye-icon {
            font-size: 0.9em;
            opacity: 0.4;
        }

        .eye-icon:hover {
            opacity: 0.6;
        }

        #save-api-key {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }

        #save-api-key:hover {
            background-color: #45a049;
        }

        /* 用户反馈样式 */
        .feedback-section {
            margin-top: 20px;
        }

        .feedback-section h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .feedback-form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        #feedback-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 60px;
            font-family: inherit;
        }

        #feedback-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
        }

        .feedback-btn, .history-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .feedback-btn {
            background-color: #2196F3;
            color: white;
        }

        .feedback-btn:hover {
            background-color: #1976D2;
        }

        .feedback-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .history-btn {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            margin-top: 10px;
            width: 100%;
        }

        .history-btn:hover {
            background-color: #e0e0e0;
        }

        /* AI工具展示区域样式 */
        .ai-tools-section {
            margin: 30px 0;
        }

        .ai-tools-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            padding: 20px 0;
            max-height: 800px;
            overflow-y: auto;
        }

        .ai-tools-container::-webkit-scrollbar {
            width: 8px;
        }

        .ai-tools-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .ai-tools-container::-webkit-scrollbar-thumb {
            background: #D4B176;
            border-radius: 10px;
        }

        .ai-tools-container::-webkit-scrollbar-thumb:hover {
            background: #E6B84D;
        }

        .ai-tool-card {
            position: relative;
            background: linear-gradient(145deg, #ffffff, #fafafa);
            padding: 25px 20px;
            border-radius: 20px;
            border: 1px solid rgba(230, 184, 77, 0.15);
            box-shadow:
                0 10px 20px rgba(0,0,0,0.05),
                0 6px 6px rgba(0,0,0,0.02),
                inset 0 -2px 5px rgba(255,255,255,0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            height: 280px;
            text-decoration: none;
            color: inherit;
        }

        .ai-tool-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 20px 40px rgba(0,0,0,0.12),
                0 15px 12px rgba(0,0,0,0.08),
                inset 0 -3px 8px rgba(255,255,255,0.8);
            border-color: rgba(230, 184, 77, 0.3);
        }

        .ai-tool-tag {
            position: absolute;
            top: 12px;
            right: 12px;
            background: linear-gradient(135deg, #E6B84D, #D4B176);
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(230, 184, 77, 0.3);
        }

        .ai-tool-icon {
            font-size: 3em;
            margin: 10px 0;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
        }

        .ai-tool-title {
            font-size: 1.3em;
            font-weight: 700;
            color: #2c3e50;
            text-align: center;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .ai-tool-description {
            font-size: 0.95em;
            color: #7f8c8d;
            text-align: center;
            margin: 0;
            line-height: 1.4;
            font-weight: 500;
        }

        .ai-usage-visit-count {
            display: flex;
            align-items: center;
            gap: 6px;
            background: rgba(230, 184, 77, 0.1);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            color: #8B7355;
            font-weight: 600;
            margin-top: auto;
        }

        .ai-tool-card.coming-soon {
            opacity: 0.6;
            cursor: not-allowed;
            background: linear-gradient(145deg, #f8f8f8, #eeeeee);
        }

        .ai-tool-card.coming-soon:hover {
            transform: none;
            box-shadow:
                0 10px 20px rgba(0,0,0,0.05),
                0 6px 6px rgba(0,0,0,0.02),
                inset 0 -2px 5px rgba(255,255,255,0.7);
        }

        .ai-tool-tag.coming-soon {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }

        .ai-tools-error {
            text-align: center;
            color: #e74c3c;
            font-size: 1.1em;
            padding: 40px 20px;
            background: #fff5f5;
            border: 1px solid #fecaca;
            border-radius: 8px;
            margin: 20px 0;
        }

        /* 强制天气卡片独占一行 */
        .weather-section {
            display: block !important;
            width: 100% !important;
            clear: both !important;
            float: none !important;
            margin-bottom: 2rem !important;
            grid-column: 1 / -1 !important; /* 如果在网格中，占满整行 */
            flex-basis: 100% !important; /* 如果在flex中，占满整行 */
        }

        .weather-card.weather-full-width {
            display: block !important;
            width: 100% !important;
            float: none !important;
            clear: both !important;
        }

        /* 防止天气卡片内部的main-content类与页面main-content冲突 */
        .weather-card .weather-main-content {
            display: grid !important;
            grid-template-columns: auto 1fr auto !important;
            gap: 32px !important;
            align-items: center !important;
        }

        /* 确保天气卡片不会与其他元素在同一行 */
        .weather-section::before,
        .weather-section::after {
            content: "";
            display: table;
            clear: both;
        }

        /* AI 助教平台主题样式 */
        .page-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .title-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            position: relative;
            z-index: 2;
            padding: 40px 20px;
        }

        .title-icon {
            font-size: 4rem;
            color: rgba(255, 255, 255, 0.9);
            animation: robotPulse 3s ease-in-out infinite;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
        }

        .title-content {
            text-align: center;
            color: white;
        }

        .title-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #ffffff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleShine 4s ease-in-out infinite;
        }

        .title-subtitle {
            font-size: 1.2rem;
            margin: 10px 0 0 0;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 400;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            letter-spacing: 2px;
        }

        .title-decoration {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .floating-icon {
            font-size: 2rem;
            color: rgba(255, 255, 255, 0.7);
            animation: float 6s ease-in-out infinite;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .floating-icon:nth-child(1) {
            animation-delay: 0s;
        }

        .floating-icon:nth-child(2) {
            animation-delay: 2s;
        }

        .floating-icon:nth-child(3) {
            animation-delay: 4s;
        }

        /* 动画效果 */
        @keyframes robotPulse {
            0%, 100% {
                transform: scale(1);
                color: rgba(255, 255, 255, 0.9);
            }
            50% {
                transform: scale(1.1);
                color: rgba(255, 255, 255, 1);
            }
        }

        @keyframes titleShine {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            33% {
                transform: translateY(-10px);
            }
            66% {
                transform: translateY(5px);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .title-container {
                flex-direction: column;
                gap: 20px;
                padding: 30px 15px;
            }

            .title-icon {
                font-size: 3rem;
            }

            .title-content h1 {
                font-size: 2.5rem;
            }

            .title-subtitle {
                font-size: 1rem;
                letter-spacing: 1px;
            }

            .title-decoration {
                flex-direction: row;
                gap: 15px;
            }

            .floating-icon {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .title-container {
                padding: 25px 10px;
            }

            .title-content h1 {
                font-size: 2rem;
            }

            .title-subtitle {
                font-size: 0.9rem;
            }

            .floating-icon {
                font-size: 1.2rem;
            }
        }

        /* 学习指南样式 */
        .usage-instructions {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 24px;
            margin-top: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .instructions-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            font-size: 1.3rem;
            color: #495057;
            border-bottom: 2px solid #667eea;
            padding-bottom: 12px;
        }

        .instructions-header i {
            color: #667eea;
            font-size: 1.5rem;
        }

        .instructions-content {
            display: grid;
            gap: 16px;
        }

        .instruction-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .instruction-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
        }

        .instruction-item i {
            color: #667eea;
            font-size: 1.2rem;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .instruction-item strong {
            color: #495057;
            margin-right: 8px;
        }

        /* 功能卡片增强样式 */
        .feature-card {
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .feature-description {
            color: #6c757d;
            font-size: 0.95rem;
            margin: 10px 0 20px 0;
            line-height: 1.5;
            text-align: center;
        }

        .knowledge-section h4 {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
            margin-bottom: 15px;
        }

        .knowledge-section h4 i {
            color: #667eea;
        }

        .upload-btn, .feature-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .upload-info {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.9rem;
        }

        .upload-info i {
            color: #6c757d;
        }

        .knowledge-access p {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .knowledge-access p i {
            color: #667eea;
        }

        /* 总访问次数显示样式 */
        .total-visits-display {
            position: fixed;
            top: 10px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .total-visits-display i {
            font-size: 1.1em;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 访问记录模态框样式 */
        .access-log-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .access-log-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .access-log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .access-log-close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s;
        }

        .access-log-close:hover {
            color: #667eea;
        }

        .access-log-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .access-stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .access-stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .access-stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .access-log-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .access-log-table th,
        .access-log-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .access-log-table th {
            background-color: #667eea;
            color: white;
            font-weight: 600;
        }

        .access-log-table tr:hover {
            background-color: #f8f9fa;
        }

        .user-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .user-badge.admin {
            background-color: #dc3545;
            color: white;
        }

        .user-badge.anonymous {
            background-color: #6c757d;
            color: white;
        }

        .user-badge.user {
            background-color: #28a745;
            color: white;
        }

        /* 网站作者信息样式 */
        .author-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin: 2rem 0;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            width: 100%;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .author-content {
            display: flex !important;
            align-items: center;
            justify-content: center;
            gap: 12px;
            font-size: 1rem;
            color: #495057;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .author-content i {
            color: #667eea;
            font-size: 1.2rem;
        }

        .author-email {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .author-email:hover {
            color: #764ba2;
            background-color: rgba(102, 126, 234, 0.1);
            text-decoration: underline;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .instructions-content {
                gap: 12px;
            }

            .instruction-item {
                padding: 12px;
                flex-direction: column;
                text-align: center;
            }

            .instruction-item i {
                margin-top: 0;
                margin-bottom: 8px;
            }

            .total-visits-display {
                top: 5px;
                right: 10px;
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .access-log-content {
                width: 95%;
                margin: 2% auto;
                padding: 15px;
            }

            .access-log-stats {
                grid-template-columns: 1fr;
            }

            .author-content {
                flex-direction: column;
                gap: 8px;
            }
        }

        /* 天气卡片样式 - 增强版 */
        .weather-card {
            border-radius: 28px;
            padding: 28px;
            color: #ffffff;
            font-family: 'SF Pro Display', 'Inter', 'Helvetica Neue', 'Arial', sans-serif;
            font-weight: 400;
            width: 100%;
            max-width: 1200px;
            min-height: 320px;
            height: auto;
            margin: 0 auto;
            box-shadow:
                0 25px 80px rgba(0,0,0,0.18),
                0 12px 40px rgba(0,0,0,0.15),
                0 6px 20px rgba(0,0,0,0.12),
                inset 0 1px 0 rgba(255,255,255,0.4);
            background: linear-gradient(135deg,
                rgba(102, 126, 234, 0.95) 0%,
                rgba(118, 75, 162, 0.95) 50%,
                rgba(64, 93, 230, 0.95) 100%);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(25px);
            border: 2px solid rgba(255,255,255,0.3);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
            cursor: pointer;
            transform-style: preserve-3d;
        }

        .weather-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(255,255,255,0.1) 0%,
                transparent 30%,
                transparent 70%,
                rgba(255,255,255,0.1) 100%);
            opacity: 0;
            transition: opacity 0.6s ease;
            pointer-events: none;
        }

        .weather-card:hover {
            transform: translateY(-8px) rotateX(2deg) rotateY(2deg);
            box-shadow:
                0 30px 80px rgba(0,0,0,0.2),
                0 15px 40px rgba(0,0,0,0.15),
                0 8px 20px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.4);
            border-color: rgba(255,255,255,0.4);
        }

        .weather-card:hover::before {
            opacity: 1;
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            flex-shrink: 0;
            position: relative;
            z-index: 10;
        }

        .weather-card .location {
            font-size: 24px;
            font-weight: 600;
            font-family: 'SF Pro Display', 'Inter', sans-serif;
            color: rgba(255, 255, 255, 0.98);
            display: flex;
            align-items: center;
            gap: 12px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.4);
            letter-spacing: -0.5px;
            animation: locationPulse 4s ease-in-out infinite;
        }

        .weather-card .location i {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.3));
            animation: iconBounce 3s ease-in-out infinite;
        }

        .weather-card .time {
            font-size: 15px;
            font-weight: 500;
            font-family: 'SF Pro Text', 'Inter', sans-serif;
            color: rgba(255, 255, 255, 0.95);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
            padding: 12px 20px;
            border-radius: 28px;
            border: 1px solid rgba(255, 255, 255, 0.35);
            backdrop-filter: blur(20px);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 0.3px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .weather-card .time::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .weather-card .time:hover::before {
            left: 100%;
        }

        .weather-card .weather-main-content {
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 36px;
            align-items: center;
            flex: 1;
            min-height: 0;
            position: relative;
            z-index: 10;
        }

        .temperature-section {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
            min-width: 220px;
            position: relative;
        }

        .weather-card .temperature {
            font-size: 64px;
            font-weight: 100;
            font-family: 'SF Pro Display', 'Helvetica Neue', sans-serif;
            color: rgba(255, 255, 255, 0.98);
            display: flex;
            align-items: baseline;
            gap: 8px;
            line-height: 0.9;
            margin-bottom: 16px;
            text-shadow: 0 8px 16px rgba(0,0,0,0.5);
            letter-spacing: -3px;
            animation: temperaturePulse 6s ease-in-out infinite;
            position: relative;
        }

        .weather-card .temperature::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, rgba(255,255,255,0.8), rgba(255,255,255,0.3));
            border-radius: 2px;
            animation: temperatureUnderline 3s ease-in-out infinite;
        }

        .weather-card .temperature .unit {
            font-size: 32px;
            font-weight: 200;
            font-family: 'SF Pro Display', sans-serif;
            color: rgba(255, 255, 255, 0.85);
            margin-left: -8px;
            animation: unitFloat 4s ease-in-out infinite;
        }

        .feels-like {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.88);
            font-weight: 500;
            font-family: 'SF Pro Text', 'Inter', sans-serif;
            margin-bottom: 14px;
            text-shadow: 0 3px 6px rgba(0,0,0,0.4);
            letter-spacing: 0.2px;
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .weather-card .weather-description {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            font-family: 'SF Pro Text', 'Inter', sans-serif;
            text-shadow: 0 4px 8px rgba(0,0,0,0.4);
            letter-spacing: 0.3px;
            animation: fadeInUp 1s ease-out 0.7s both;
        }

        .weather-card .weather-description i {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.3));
            animation: iconSpin 8s linear infinite;
        }

        .weather-visual {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            min-width: 100px;
        }

        .weather-card .weather-icon {
            font-size: 80px;
            color: rgba(255, 255, 255, 0.95);
            filter: drop-shadow(0 8px 16px rgba(0,0,0,0.4));
            animation: weatherFloat 4s ease-in-out infinite, iconGlow 6s ease-in-out infinite alternate;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .weather-card .weather-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 120%;
            height: 120%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .weather-card .weather-icon:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .weather-card .weather-icon:hover::before {
            opacity: 1;
        }

        .weather-details {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 16px;
            flex: 1;
            min-width: 0;
            position: relative;
            z-index: 10;
        }

        .weather-card .detail-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            padding: 16px 10px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
            border-radius: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(15px);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-width: 0;
            transform-style: preserve-3d;
        }

        .weather-card .detail-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .weather-card .detail-item::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .weather-card .detail-item:hover {
            transform: translateY(-8px) scale(1.05) rotateX(5deg);
            box-shadow:
                0 20px 40px rgba(0,0,0,0.2),
                0 10px 20px rgba(0,0,0,0.15);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .weather-card .detail-item:hover::before {
            opacity: 1;
        }

        .weather-card .detail-item:hover::after {
            opacity: 1;
            transform: rotate(45deg) translate(50%, 50%);
        }

        .weather-card .detail-item i {
            font-size: 22px;
            color: rgba(255, 255, 255, 0.95);
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.3));
            margin-bottom: 4px;
            animation: iconPulse 3s ease-in-out infinite;
            transition: all 0.3s ease;
        }

        .weather-card .detail-item:hover i {
            transform: scale(1.2) rotate(10deg);
            color: rgba(255, 255, 255, 1);
        }

        .detail-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.82);
            font-weight: 600;
            font-family: 'SF Pro Text', 'Inter', sans-serif;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 6px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .weather-card .detail-item:hover .detail-label {
            color: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        }

        .detail-value {
            font-size: 17px;
            color: rgba(255, 255, 255, 0.98);
            font-weight: 600;
            font-family: 'SF Pro Display', 'Helvetica Neue', sans-serif;
            text-shadow: 0 3px 6px rgba(0,0,0,0.4);
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            letter-spacing: 0.2px;
            transition: all 0.3s ease;
        }

        .weather-card .detail-item:hover .detail-value {
            transform: translateY(-2px) scale(1.05);
            text-shadow: 0 5px 10px rgba(0,0,0,0.5);
        }

        /* 天气动画效果 - 增强版 */
        @keyframes weatherFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-8px) rotate(2deg); }
            50% { transform: translateY(-15px) rotate(0deg); }
            75% { transform: translateY(-8px) rotate(-2deg); }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes locationPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes iconBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-3px); }
        }

        @keyframes temperaturePulse {
            0%, 100% { transform: scale(1); text-shadow: 0 6px 12px rgba(0,0,0,0.4); }
            50% { transform: scale(1.02); text-shadow: 0 8px 16px rgba(0,0,0,0.5); }
        }

        @keyframes temperatureUnderline {
            0%, 100% { width: 0; }
            50% { width: 80%; }
        }

        @keyframes unitFloat {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-2px); }
        }

        @keyframes iconSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes iconGlow {
            0% { filter: drop-shadow(0 8px 16px rgba(0,0,0,0.4)); }
            100% { filter: drop-shadow(0 12px 24px rgba(255,255,255,0.3)); }
        }

        @keyframes iconPulse {
            0%, 100% { opacity: 0.9; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }

        .weather-card {
            animation: slideInUp 1s ease-out;
        }

        .weather-card .detail-item {
            animation: slideInUp 0.8s ease-out;
            animation-fill-mode: both;
        }

        .weather-card .detail-item:nth-child(1) { animation-delay: 0.2s; }
        .weather-card .detail-item:nth-child(2) { animation-delay: 0.3s; }
        .weather-card .detail-item:nth-child(3) { animation-delay: 0.4s; }
        .weather-card .detail-item:nth-child(4) { animation-delay: 0.5s; }
        .weather-card .detail-item:nth-child(5) { animation-delay: 0.6s; }
        .weather-card .detail-item:nth-child(6) { animation-delay: 0.7s; }

        /* 天气骨架屏样式 */
        .weather-skeleton {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            animation: fadeIn 0.3s ease-in-out;
        }

        .skeleton-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
            color: transparent !important;
            box-shadow: 0 8px 20px rgba(187, 222, 251, 0.3) !important;
        }

        .skeleton-text {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
            height: 1em;
            margin: 2px 0;
            color: transparent !important;
        }

        .skeleton-location {
            width: 60%;
            height: 1.2em;
        }

        .skeleton-time {
            width: 40%;
            height: 1em;
        }

        .skeleton-temp {
            width: 80px;
            height: 2.5em;
            margin-bottom: 8px;
        }

        .skeleton-feels {
            width: 120px;
            height: 1em;
        }

        .skeleton-desc {
            width: 100px;
            height: 1em;
        }

        .skeleton-label {
            width: 40px;
            height: 0.8em;
        }

        .skeleton-value {
            width: 50px;
            height: 0.8em;
        }

        .skeleton-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 50%;
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* 天气错误状态样式 */
        .weather-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
            background: linear-gradient(135deg, #ff7675 0%, #e17055 100%);
            border-radius: 20px;
            color: white;
            min-height: 200px;
            box-shadow: 0 8px 20px rgba(255, 118, 117, 0.3);
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.9;
        }

        .error-message {
            font-size: 16px;
            margin-bottom: 20px;
            font-weight: 500;
            line-height: 1.4;
        }

        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* 天气特效元素 - 增强版 */
        .weather-effect {
            position: absolute;
            pointer-events: none;
            z-index: 2;
        }

        /* 粒子系统基础 */
        .particle-system {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            opacity: 0.6;
            animation: particleFloat 8s ease-in-out infinite;
        }

        /* 太阳效果 - 增强版 */
        .sun-rays {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: radial-gradient(circle,
                rgba(255, 223, 0, 0.9) 0%,
                rgba(255, 165, 0, 0.6) 30%,
                rgba(255, 140, 0, 0.3) 60%,
                transparent 80%);
            animation: sunRotate 25s linear infinite, sunPulse 6s ease-in-out infinite alternate;
            box-shadow: 0 0 40px rgba(255, 223, 0, 0.4);
        }

        .sun-rays::before {
            content: '';
            position: absolute;
            top: -30px;
            left: -30px;
            right: -30px;
            bottom: -30px;
            background:
                linear-gradient(0deg, transparent 40%, rgba(255, 223, 0, 0.4) 50%, transparent 60%),
                linear-gradient(30deg, transparent 40%, rgba(255, 223, 0, 0.3) 50%, transparent 60%),
                linear-gradient(60deg, transparent 40%, rgba(255, 223, 0, 0.4) 50%, transparent 60%),
                linear-gradient(90deg, transparent 40%, rgba(255, 223, 0, 0.3) 50%, transparent 60%),
                linear-gradient(120deg, transparent 40%, rgba(255, 223, 0, 0.4) 50%, transparent 60%),
                linear-gradient(150deg, transparent 40%, rgba(255, 223, 0, 0.3) 50%, transparent 60%);
            animation: sunRaysPulse 5s ease-in-out infinite alternate;
        }

        .sun-rays::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 223, 0, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: sunAura 8s ease-in-out infinite;
        }

        /* 月亮和星星效果 - 增强版 */
        .moon {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: radial-gradient(circle at 35% 35%,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(240, 240, 255, 0.8) 40%,
                rgba(220, 220, 255, 0.6) 80%,
                rgba(200, 200, 255, 0.3) 100%);
            box-shadow:
                0 0 30px rgba(255, 255, 255, 0.6),
                inset -10px -10px 0 rgba(200, 200, 255, 0.2);
            animation: moonGlow 8s ease-in-out infinite alternate, moonFloat 12s ease-in-out infinite;
        }

        .moon::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 15px;
            width: 8px;
            height: 8px;
            background: rgba(180, 180, 220, 0.4);
            border-radius: 50%;
            box-shadow:
                20px 5px 0 2px rgba(180, 180, 220, 0.3),
                10px 20px 0 1px rgba(180, 180, 220, 0.2);
        }

        .stars {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        .star {
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            animation: twinkle 4s ease-in-out infinite;
            box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
        }

        .star::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 1px;
            height: 8px;
            background: rgba(255, 255, 255, 0.6);
            transform: translate(-50%, -50%);
        }

        .star::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 1px;
            background: rgba(255, 255, 255, 0.6);
            transform: translate(-50%, -50%);
        }

        .star:nth-child(odd) {
            animation-delay: 1s;
            animation-duration: 3s;
        }

        .star:nth-child(3n) {
            animation-delay: 2s;
            animation-duration: 5s;
        }

        .star:nth-child(4n) {
            animation-delay: 0.5s;
            animation-duration: 3.5s;
        }

        /* 雨滴效果 - 增强版 */
        .rain-drop {
            position: absolute;
            width: 3px;
            height: 20px;
            background: linear-gradient(to bottom,
                rgba(173, 216, 230, 0.9) 0%,
                rgba(135, 206, 235, 0.95) 50%,
                rgba(100, 180, 220, 0.8) 100%);
            border-radius: 0 0 3px 3px;
            animation: rainFall 1.2s linear infinite;
            box-shadow: 0 0 3px rgba(135, 206, 235, 0.5);
        }

        .rain-drop::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            width: 4px;
            height: 4px;
            background: rgba(173, 216, 230, 0.6);
            border-radius: 50%;
            transform: translateX(-50%);
        }

        .rain-drop:nth-child(2n) {
            animation-delay: 0.3s;
            animation-duration: 1s;
            width: 2px;
            height: 18px;
        }

        .rain-drop:nth-child(3n) {
            animation-delay: 0.6s;
            animation-duration: 1.4s;
            width: 4px;
            height: 25px;
        }

        .rain-drop:nth-child(4n) {
            animation-delay: 0.9s;
            animation-duration: 0.9s;
            width: 2.5px;
            height: 22px;
        }

        /* 雪花效果 - 增强版 */
        .snowflake {
            position: absolute;
            color: rgba(255, 255, 255, 0.95);
            font-size: 16px;
            animation: snowFall 10s linear infinite;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
            filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.6));
        }

        .snowflake::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: snowGlow 3s ease-in-out infinite alternate;
        }

        .snowflake:nth-child(2n) {
            animation-delay: 2s;
            animation-duration: 8s;
            font-size: 14px;
        }

        .snowflake:nth-child(3n) {
            animation-delay: 4s;
            animation-duration: 12s;
            font-size: 18px;
        }

        .snowflake:nth-child(4n) {
            animation-delay: 6s;
            animation-duration: 9s;
            font-size: 13px;
        }

        .snowflake:nth-child(5n) {
            animation-delay: 1s;
            animation-duration: 11s;
            font-size: 20px;
        }

        /* 云朵效果 - 增强版 */
        .cloud {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(240, 240, 255, 0.2));
            border-radius: 50px;
            animation: cloudFloat 18s ease-in-out infinite;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(2px);
        }

        .cloud::before,
        .cloud::after {
            content: '';
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.35), rgba(240, 240, 255, 0.15));
            border-radius: 50px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .cloud-1 {
            width: 100px;
            height: 35px;
            top: 30px;
            left: 15px;
            animation-duration: 20s;
        }

        .cloud-1::before {
            width: 50px;
            height: 50px;
            top: -25px;
            left: 15px;
        }

        .cloud-1::after {
            width: 60px;
            height: 40px;
            top: -20px;
            right: 10px;
        }

        .cloud-2 {
            width: 75px;
            height: 30px;
            top: 70px;
            right: 30px;
            animation-delay: 6s;
            animation-duration: 16s;
        }

        .cloud-2::before {
            width: 35px;
            height: 35px;
            top: -18px;
            left: 20px;
        }

        .cloud-2::after {
            width: 45px;
            height: 30px;
            top: -15px;
            right: 15px;
        }

        .cloud-3 {
            width: 60px;
            height: 25px;
            top: 120px;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 12s;
            animation-duration: 22s;
        }

        .cloud-3::before {
            width: 30px;
            height: 30px;
            top: -15px;
            left: 10px;
        }

        .cloud-3::after {
            width: 35px;
            height: 25px;
            top: -12px;
            right: 10px;
        }

        /* 动画关键帧 - 增强版 */
        @keyframes sunRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes sunPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 40px rgba(255, 223, 0, 0.4);
            }
            100% {
                transform: scale(1.1);
                box-shadow: 0 0 60px rgba(255, 223, 0, 0.6);
            }
        }

        @keyframes sunRaysPulse {
            0% { opacity: 0.4; transform: scale(1); }
            100% { opacity: 0.7; transform: scale(1.05); }
        }

        @keyframes sunAura {
            0% { opacity: 0.1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.3; transform: translate(-50%, -50%) scale(1.2); }
            100% { opacity: 0.1; transform: translate(-50%, -50%) scale(1); }
        }

        @keyframes moonGlow {
            0% {
                box-shadow: 0 0 30px rgba(255, 255, 255, 0.6);
                transform: scale(1);
            }
            100% {
                box-shadow: 0 0 50px rgba(255, 255, 255, 0.8);
                transform: scale(1.08);
            }
        }

        @keyframes moonFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-5px) rotate(2deg); }
        }

        @keyframes twinkle {
            0% { opacity: 0.4; transform: scale(1) rotate(0deg); }
            50% { opacity: 1; transform: scale(1.3) rotate(180deg); }
            100% { opacity: 0.4; transform: scale(1) rotate(360deg); }
        }

        @keyframes particleFloat {
            0%, 100% {
                transform: translateY(0) translateX(0) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-20px) translateX(10px) scale(1.1);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-10px) translateX(-5px) scale(0.9);
                opacity: 0.8;
            }
            75% {
                transform: translateY(-30px) translateX(15px) scale(1.2);
                opacity: 0.4;
            }
        }

        @keyframes rainFall {
            0% {
                transform: translateY(-120vh) translateX(0) rotate(0deg);
                opacity: 0;
            }
            5% {
                opacity: 0.8;
            }
            95% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(120vh) translateX(15px) rotate(5deg);
                opacity: 0;
            }
        }

        @keyframes snowFall {
            0% {
                transform: translateY(-120vh) translateX(0) rotate(0deg);
                opacity: 0;
            }
            5% {
                opacity: 0.9;
            }
            95% {
                opacity: 0.9;
            }
            100% {
                transform: translateY(120vh) translateX(80px) rotate(720deg);
                opacity: 0;
            }
        }

        @keyframes snowGlow {
            0% { opacity: 0.3; }
            100% { opacity: 0.8; }
        }

        @keyframes cloudFloat {
            0% {
                transform: translateX(-30px) translateY(0);
                opacity: 0.6;
            }
            25% {
                transform: translateX(10px) translateY(-5px);
                opacity: 0.8;
            }
            50% {
                transform: translateX(30px) translateY(0);
                opacity: 0.9;
            }
            75% {
                transform: translateX(10px) translateY(5px);
                opacity: 0.7;
            }
            100% {
                transform: translateX(-30px) translateY(0);
                opacity: 0.6;
            }
        }

        @keyframes splashEffect {
            0% {
                transform: scale(0) translateY(0);
                opacity: 1;
            }
            50% {
                transform: scale(1.5) translateY(-10px);
                opacity: 0.8;
            }
            100% {
                transform: scale(2) translateY(-20px);
                opacity: 0;
            }
        }

        @keyframes rippleEffect {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        /* 天气卡片响应式设计 */
        @media (max-width: 1024px) {
            .weather-details {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .weather-card {
                padding: 16px;
                min-height: 240px;
            }

            .weather-card .weather-main-content {
                grid-template-columns: 1fr;
                gap: 16px;
                text-align: center;
            }

            .temperature-section {
                min-width: auto;
                align-items: center;
            }

            .weather-visual {
                order: -1;
            }

            .weather-card .weather-icon {
                font-size: 48px;
            }

            .weather-card .temperature {
                font-size: 36px;
                justify-content: center;
            }

            .weather-details {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .weather-card {
                padding: 12px;
                min-height: 200px;
            }

            .weather-header {
                flex-direction: column;
                gap: 8px;
                text-align: center;
                margin-bottom: 12px;
            }

            .weather-card .location {
                font-size: 18px;
            }

            .weather-card .temperature {
                font-size: 32px;
            }

            .weather-card .weather-icon {
                font-size: 40px;
            }

            .weather-details {
                grid-template-columns: repeat(2, 1fr);
                gap: 6px;
            }

            .weather-card .detail-item {
                padding: 8px 4px;
            }

            .detail-label {
                font-size: 9px;
            }
        }

        /* 移除已弃用的Edge浏览器检测，改用JavaScript动态控制 */

        /* 减少动画的媒体查询 - 用户偏好设置 */
        @media (prefers-reduced-motion: reduce) {
            .weather-card,
            .weather-card .detail-item,
            .weather-effect,
            .weather-icon {
                animation: none !important;
                transition: none !important;
            }

            .weather-card:hover {
                transform: none !important;
            }

            .detail-value {
                font-size: 12px;
            }
        }
    </style>
</head>
<body class="dashboard-body">
    <!-- 总访问次数显示 -->
    <div class="total-visits-display" id="total-visits-display">
        <i class="fas fa-eye"></i>
        <span>总访问: <span id="total-visits-count">加载中...</span></span>
    </div>

    <!-- 滚动文字 -->
    <div class="scrolling-text-container">
        <div class="scrolling-text">
            🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <!-- 用户欢迎信息 -->
        {% if is_vip %}
        <div class="user-welcome vip-welcome">
            <div class="user-welcome-text">
                <span style="font-size: 1.2em; color: #FFD700;">👑</span> 
                尊敬的会员，欢迎回来
            </div>
            <div class="user-name vip-user">
                <span class="vip-username">{{ username }}</span>
                <span class="vip-badge">
                    <span style="color: #8B4513;">♛</span>
                    VIP {{ vip_days_left }} 天
                </span>
            </div>
        </div>
        {% else %}
        <div class="user-welcome">
            <div class="user-welcome-text">👋 欢迎回来</div>
            <div class="user-name">{{ username }}</div>
        </div>
        {% endif %}
        
        <div class="sidebar-divider"></div>

        <!-- API密钥配置 -->
        <div class="api-config-section">
            <h4>API 密钥配置</h4>
            <div class="api-key-selector">
                <label for="api-key-type">选择 API 密钥类型:</label>
                <select id="api-key-type">
                    <option value="siliconflow">Siliconflow</option>
                    <option value="deepseek">Deepseek</option>
                    <option value="qwen">通义千问</option>
                </select>
            </div>
            <div class="api-key-input">
                <div class="input-wrapper">
                    <input type="password" id="api-key-input" placeholder="输入您的 API 密钥">
                    <button type="button" id="toggle-password" class="toggle-password">
                        <span class="eye-icon">👁️</span>
                    </button>
                </div>
                <button id="save-api-key">保存</button>
            </div>
            
            <!-- API密钥申请链接 -->
            <div class="api-links-container" id="api-links-container">
                <div class="api-link-title">🔑 获取 API 密钥</div>
                <a href="https://platform.deepseek.com/api_keys" target="_blank" class="api-link-button">
                    <span class="api-link-icon">🚀</span> Deepseek API 密钥申请
                </a>
                <a href="https://cloud.siliconflow.cn/account/ak" target="_blank" class="api-link-button">
                    <span class="api-link-icon">⚡</span> Siliconflow API 密钥申请
                </a>
                <a href="https://bailian.console.aliyun.com/?apiKey=1#/api-key" target="_blank" class="api-link-button">
                    <span class="api-link-icon">🔮</span> 阿里云通义千问 API 密钥申请
                </a>
            </div>
        </div>

        <div class="sidebar-divider"></div>
        <!-- 管理员链接 - 只有管理员可见 -->
        <div class="admin-section" id="admin-section" style="display: none;">
            <h4>管理员功能</h4>
            <div class="admin-links">
                <a href="#" id="admin-page-link" class="admin-link" target="_blank">
                    <span class="admin-icon">👑</span> 管理员页面
                </a>
                <a href="#" id="admin-classes-link" class="admin-link" target="_blank">
                    <span class="admin-icon">📊</span> 班级学习统计
                </a>
                <a href="#" id="admin-dashboard-link" class="admin-link" target="_blank">
                    <span class="admin-icon">📈</span> 管理员仪表盘
                </a>
                <a href="#" id="access-log-link" class="admin-link" onclick="showAccessLog()">
                    <span class="admin-icon">📋</span> 访问记录统计
                </a>
            </div>
        </div>

        <div class="sidebar-divider" id="admin-divider" style="display: none;"></div>

        <!-- 用户反馈区域 -->
        <div class="feedback-section">
            <h4>💬 用户反馈</h4>
            <div class="feedback-form">
                <textarea id="feedback-input" placeholder="请输入您的反馈意见..." rows="3"></textarea>
                <button id="submit-feedback" class="feedback-btn">提交反馈</button>
            </div>
            <div class="feedback-history" id="feedback-history-section">
                <button id="view-feedback-history" class="history-btn">查看历史反馈</button>
            </div>
        </div>

        <div class="sidebar-divider"></div>

        <!-- 退出按钮 -->
        <div class="logout-section">
            <a href="/main/logout" class="logout-btn">🚪 退出登录</a>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 页面标题 -->
        <div class="page-title">
            <div class="title-container">
                <div class="title-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="title-content">
                    <h1>AI 助教平台</h1>
                    <p class="title-subtitle">智能学习 · 个性化辅导 · 高效提升</p>
                </div>
                <div class="title-decoration">
                    <div class="floating-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="floating-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="floating-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 天气信息 - 单独一行 -->
        <div class="weather-section">
            <div class="feature-card weather-card weather-full-width">
                <div id="weather-info">
                    <!-- 预加载的天气卡片骨架 -->
                    <div class="weather-skeleton" id="weather-skeleton">
                        <div class="weather-card skeleton-card">
                            <div class="weather-header">
                                <div class="location skeleton-text skeleton-location"></div>
                                <div class="time skeleton-text skeleton-time"></div>
                            </div>
                            <div class="weather-main-content">
                                <div class="temperature-section">
                                    <div class="temperature skeleton-text skeleton-temp"></div>
                                    <div class="feels-like skeleton-text skeleton-feels"></div>
                                    <div class="weather-description skeleton-text skeleton-desc"></div>
                                </div>
                                <div class="weather-details">
                                    <div class="detail-item">
                                        <div class="detail-label skeleton-text skeleton-label"></div>
                                        <div class="detail-value skeleton-text skeleton-value"></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label skeleton-text skeleton-label"></div>
                                        <div class="detail-value skeleton-text skeleton-value"></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label skeleton-text skeleton-label"></div>
                                        <div class="detail-value skeleton-text skeleton-value"></div>
                                    </div>
                                </div>
                                <div class="weather-visual">
                                    <div class="weather-icon skeleton-icon"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习资源入口 -->
        <div class="entrance-section">
            <h2 class="section-title">
                <i class="fas fa-graduation-cap"></i> 学习资源中心
            </h2>
            <div class="entrance-grid" id="entrance-grid">
                <!-- 入口按钮将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- AI工具展示区域 -->
        <div class="ai-tools-section">
            <h2 class="section-title">
                <i class="fas fa-robot"></i> AI 智能助手
            </h2>
            <div class="ai-tools-container" id="ai-tools-container">
                <!-- AI工具将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 智能学习系统 -->
        <div class="feature-grid">
            <!-- 知识库管理 -->
            <div class="feature-card knowledge-card">
                <div class="feature-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3>
                    <i class="fas fa-database"></i> 智能知识库系统
                </h3>
                <p class="feature-description">
                    上传学习资料，构建个人知识库，享受AI驱动的智能问答体验
                </p>
                <div class="knowledge-sections">
                    <div class="knowledge-section">
                        <h4>
                            <i class="fas fa-upload"></i> 资料上传中心
                        </h4>
                        <div class="upload-section">
                            <input type="file" id="file-upload" accept=".docx,.xlsx,.pptx,.jpg,.jpeg,.png,.pdf,.txt,.md,.json,.eml" style="display: none;">
                            <button class="upload-btn" onclick="document.getElementById('file-upload').click()">
                                <i class="fas fa-file-upload"></i> 选择学习资料
                            </button>
                            <div class="upload-info">
                                <i class="fas fa-info-circle"></i> 剩余上传次数: {{ remaining_uploads }}
                            </div>
                            <div class="upload-info">
                                <i class="fas fa-clock"></i> 文件处理需要约5分钟，请耐心等待
                            </div>
                        </div>
                    </div>

                    <div class="knowledge-section">
                        <h4>
                            <i class="fas fa-comments"></i> AI 智能问答
                        </h4>
                        <div class="knowledge-access">
                            <p>
                                <i class="fas fa-magic"></i> 与您的专属AI助教对话，获得个性化学习指导
                            </p>
                            <button class="feature-btn" onclick="openKnowledgeBase()">
                                <i class="fas fa-rocket"></i> 开始智能学习
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习指南 -->
        <div class="usage-instructions">
            <div class="instructions-header">
                <i class="fas fa-lightbulb"></i>
                <strong>AI 助教平台使用指南</strong>
            </div>
            <div class="instructions-content">
                <div class="instruction-item">
                    <i class="fas fa-search-plus"></i>
                    <strong>深度搜索功能</strong>：启用后可获得更精准的学习内容推荐，但会消耗更多AI资源
                </div>
                <div class="instruction-item">
                    <i class="fas fa-cogs"></i>
                    <strong>智能适配</strong>：系统会根据您的学习习惯和语言偏好，自动调整输出内容
                </div>
                <div class="instruction-item">
                    <i class="fas fa-graduation-cap"></i>
                    <strong>个性化学习</strong>：上传您的学习资料，AI助教将为您提供专属的学习指导
                </div>
                <div class="instruction-item">
                    <i class="fas fa-chart-line"></i>
                    <strong>学习进度</strong>：系统会记录您的学习轨迹，帮助您更好地掌握知识点
                </div>
            </div>
        </div>

        <!-- 网站作者信息 -->
        <div class="author-info" style="display: block !important; visibility: visible !important; opacity: 1 !important; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: 1px solid #dee2e6; border-radius: 12px; padding: 20px; margin: 2rem 0; text-align: center; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
            <div class="author-content" style="display: flex !important; align-items: center; justify-content: center; gap: 12px; font-size: 1rem; color: #495057;">
                <i class="fas fa-envelope" style="color: #667eea; font-size: 1.2rem;"></i>
                <span>网站作者邮箱：</span>
                <a href="mailto:<EMAIL>" class="author-email" style="color: #667eea; text-decoration: none; font-weight: 600;"><EMAIL></a>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="message-container" class="message-container"></div>

    <!-- 访问记录模态框 -->
    <div id="access-log-modal" class="access-log-modal">
        <div class="access-log-content">
            <div class="access-log-header">
                <h2><i class="fas fa-chart-bar"></i> 访问记录统计</h2>
                <span class="access-log-close" onclick="closeAccessLog()">&times;</span>
            </div>
            <div class="access-log-stats" id="access-log-stats">
                <!-- 统计卡片将通过JavaScript动态加载 -->
            </div>
            <div class="access-log-table-container">
                <table class="access-log-table">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>访问次数</th>
                            <th>首次访问</th>
                            <th>最近访问</th>
                        </tr>
                    </thead>
                    <tbody id="access-log-table-body">
                        <!-- 表格内容将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="{{ url_for('main.static', filename='js/dashboard.js') }}"></script>
    <script>
        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadFile(file);
            }
        });

        async function uploadFile(file) {
            // 添加文件类型检查
            const allowedExtensions = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

            console.log('File name:', file.name);
            console.log('File extension:', fileExtension);
            console.log('Allowed extensions:', allowedExtensions);

            if (!allowedExtensions.includes(fileExtension)) {
                showMessage(`不支持的文件类型: ${fileExtension}。支持的类型: ${allowedExtensions.join(', ')}`, 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                showMessage('正在上传文件...', 'info');

                console.log('Uploading to URL: /api/upload-file');
                console.log('File size:', file.size);
                console.log('File type:', file.type);

                const response = await fetch('/api/upload-file', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin' // 确保发送会话cookie
                });

                console.log('Response status:', response.status);
                console.log('Response URL:', response.url);

                // 先获取响应文本，然后尝试解析JSON
                const responseText = await response.text();
                console.log('Response text:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (jsonError) {
                    console.error('JSON parse error:', jsonError);
                    console.error('Raw response:', responseText);

                    // 如果JSON解析失败但状态码是200，可能是代理问题
                    if (response.status === 200) {
                        showMessage('上传可能成功，但响应格式异常', 'warning');
                        return;
                    }
                    throw new Error(`Invalid JSON response: ${jsonError.message}`);
                }

                // 检查业务逻辑成功状态，而不仅仅是HTTP状态码
                if (result.success || (response.status === 200 && result.message && result.message.includes('上传成功'))) {
                    showMessage(result.message || `文件 "${file.name}" 上传成功！`, 'success');
                    // 更新剩余上传次数
                    if (result.remaining_uploads !== undefined) {
                        updateRemainingUploads(result.remaining_uploads);
                    }

                    // 清空文件输入框
                    document.getElementById('file-upload').value = '';

                    console.log('Upload completed successfully');
                } else {
                    showMessage(result.message || '文件上传失败', 'error');
                }
            } catch (error) {
                console.error('Upload error:', error);

                // 检查是否是网络错误但实际上传可能成功的情况
                if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                    showMessage('网络请求异常，但文件可能已上传成功', 'warning');
                } else {
                    showMessage(`文件上传失败: ${error.message}`, 'error');
                }
            }
        }

        function updateRemainingUploads(remaining) {
            // 如果是管理员的大数字，显示为"无限制"
            const displayText = remaining >= 999999 ? '无限制' : remaining;
            document.querySelector('.upload-info').textContent = `剩余上传次数: ${displayText}`;
        }

        
        // API密钥类型选择事件
        document.getElementById('api-key-type').addEventListener('change', async function() {
            const keyType = this.value;
            const apiKeyInput = document.getElementById('api-key-input');

            // 显示加载状态
            apiKeyInput.placeholder = '正在加载...';
            apiKeyInput.disabled = true;

            try {
                await loadApiKey(keyType);
            } finally {
                // 恢复输入框状态
                apiKeyInput.placeholder = '输入您的 API 密钥';
                apiKeyInput.disabled = false;
            }
        });

        // 初始化API密钥类型
        async function initializeApiKeyType() {
            try {
                // 添加时间戳参数防止缓存
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/get-api-key-type?t=${timestamp}`);
                const result = await response.json();

                const apiKeyTypeSelect = document.getElementById('api-key-type');
                let keyType = 'siliconflow'; // 默认值

                if (result.success && result.api_key_type) {
                    keyType = result.api_key_type;
                    console.log('从后端获取的API密钥类型:', keyType);
                } else {
                    console.log('使用默认API密钥类型:', keyType);
                }

                // 设置选择框的值
                apiKeyTypeSelect.value = keyType;

                // 加载对应的API密钥
                await loadApiKey(keyType);
            } catch (error) {
                console.error('初始化API密钥类型错误:', error);
                // 出错时使用默认值
                const apiKeyTypeSelect = document.getElementById('api-key-type');
                const defaultKeyType = 'siliconflow';
                apiKeyTypeSelect.value = defaultKeyType;
                await loadApiKey(defaultKeyType);
            }
        }

        // 加载API密钥
        async function loadApiKey(keyType) {
            try {
                // 添加时间戳参数防止缓存
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/get-api-key?key_type=${keyType}&t=${timestamp}`);
                const result = await response.json();

                const apiKeyInput = document.getElementById('api-key-input');
                if (result.success) {
                    // 只有当输入框为空时才设置值
                    if (!apiKeyInput.value) {
                        apiKeyInput.value = result.api_key || '';
                    }
                    // 如果有密钥，显示提示信息
                    if (result.api_key) {
                        console.log(`已加载 ${keyType} API密钥`);
                    }
                } else {
                    // 只有当输入框为空时才清空
                    if (!apiKeyInput.value) {
                        apiKeyInput.value = '';
                    }
                    console.log(`未找到 ${keyType} API密钥`);
                }
            } catch (error) {
                console.error('加载API密钥错误:', error);
                // 只有当输入框为空时才清空
                const apiKeyInput = document.getElementById('api-key-input');
                if (!apiKeyInput.value) {
                    apiKeyInput.value = '';
                }
            }
        }

        // API密钥保存
        document.getElementById('save-api-key').addEventListener('click', async function() {
            const keyType = document.getElementById('api-key-type').value;
            const keyValue = document.getElementById('api-key-input').value.trim();
            const saveButton = this;
            const apiKeyInput = document.getElementById('api-key-input');

            if (!keyValue) {
                showMessage('请输入API密钥', 'error');
                return;
            }

            // 禁用保存按钮，防止重复提交
            saveButton.disabled = true;
            saveButton.textContent = '保存中...';

            try {
                const response = await fetch('/api/save-api-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        key_type: keyType,
                        key_value: keyValue
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(`${getKeyTypeName(keyType)} API密钥保存成功`, 'success');
                    console.log(`${keyType} API密钥已保存`);
                    // 保持输入框的值
                    apiKeyInput.value = keyValue;
                } else {
                    showMessage(result.message || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存API密钥错误:', error);
                showMessage('保存失败，请重试', 'error');
            } finally {
                // 恢复保存按钮状态
                saveButton.disabled = false;
                saveButton.textContent = '保存';
            }
        });

        // 获取API密钥类型的显示名称
        function getKeyTypeName(keyType) {
            const typeNames = {
                'siliconflow': 'Siliconflow',
                'deepseek': 'Deepseek',
                'qwen': '通义千问'
            };
            return typeNames[keyType] || keyType;
        }

        async function openKnowledgeBase() {
            try {
                const response = await fetch('/api/knowledge-base');
                const result = await response.json();

                if (result.success) {
                    window.open(result.url, '_blank');
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('打开知识库失败', 'error');
            }
        }

        // 消息显示函数
        function showMessage(message, type) {
            const container = document.getElementById('message-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                messageDiv.classList.remove('show');
                setTimeout(() => {
                    container.removeChild(messageDiv);
                }, 300);
            }, 3000);
        }

        // 加载天气信息 - 防undefined版本
        async function loadWeather() {
            const weatherContainer = document.getElementById('weather-info');
            let skeleton = document.getElementById('weather-skeleton');

            console.log('开始加载天气信息...');

            // 如果没有骨架屏，创建一个
            if (!skeleton) {
                showWeatherSkeleton();
                skeleton = document.getElementById('weather-skeleton');
            }

            // 确保骨架屏显示
            if (skeleton) {
                skeleton.style.display = 'block';
                console.log('骨架屏已显示');
            }

            try {
                console.log('发送天气API请求...');
                const response = await fetch(`/api/weather?t=${Date.now()}`);
                console.log('收到响应，状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('天气API响应:', result);

                if (result.success && result.weather_info) {
                    console.log('天气数据有效，开始渲染:', result.weather_info);
                    renderWeatherCard(result.weather_info);
                } else {
                    console.error('API返回失败或数据为空，使用模拟数据:', result);
                    // 延迟一下再显示模拟数据，确保用户看到加载过程
                    setTimeout(() => {
                        useMockWeatherData();
                    }, 500);
                }
            } catch (error) {
                console.error('天气加载错误，使用模拟数据:', error);
                // 延迟一下再显示模拟数据，确保用户看到加载过程
                setTimeout(() => {
                    useMockWeatherData();
                }, 500);
            }
        }

        // 使用模拟天气数据
        function useMockWeatherData() {
            console.log('显示模拟天气数据');
            const mockWeatherData = {
                city: '西安',
                country: '中国',
                local_time: new Date().toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                }).replace(/\//g, '-'),
                temperature: 22,
                feels_like: 24,
                humidity: 65,
                weather_description: '晴',
                wind_speed: 3.2,
                wind_deg: 180,
                clouds: 20,
                visibility: 10,
                precipitation: 0,
                sunrise: '06:30',
                sunset: '18:45'
            };

            renderWeatherCard(mockWeatherData);
        }

        // 显示天气错误信息 - 已禁用，保持骨架屏显示
        function showWeatherError(message) {
            console.log('天气错误，但保持骨架屏显示:', message);
            // 不再显示错误信息，保持骨架屏
            // 可以在后台重试加载
            setTimeout(() => {
                useMockWeatherData();
            }, 2000);
        }

        // 渲染天气卡片 - 防undefined版本
        function renderWeatherCard(weatherInfo) {
            const weatherContainer = document.getElementById('weather-info');
            const skeleton = document.getElementById('weather-skeleton');

            console.log('开始渲染天气卡片，数据:', weatherInfo);

            // 简单验证
            if (!weatherInfo || !weatherInfo.city) {
                console.error('天气数据无效，保持骨架屏显示');
                return; // 保持骨架屏，不显示错误
            }

            // 隐藏骨架屏
            if (skeleton) {
                skeleton.style.display = 'none';
                console.log('骨架屏已隐藏');
            }

            // 设置安全的默认值
            const safeWeatherInfo = {
                city: weatherInfo.city || '未知城市',
                country: weatherInfo.country || '',
                local_time: weatherInfo.local_time || new Date().toLocaleString(),
                temperature: weatherInfo.temperature || 0,
                feels_like: weatherInfo.feels_like || weatherInfo.temperature || 0,
                humidity: weatherInfo.humidity || 0,
                weather_description: weatherInfo.weather_description || '晴',
                wind_speed: weatherInfo.wind_speed || 0,
                wind_deg: weatherInfo.wind_deg || 0,
                clouds: weatherInfo.clouds || 0,
                visibility: weatherInfo.visibility || 0,
                precipitation: weatherInfo.precipitation || 0
            };

            console.log('处理后的安全天气数据:', safeWeatherInfo);

            const weatherHTML = `
                <div class="weather-card" id="weather-card">
                    <div class="weather-header">
                        <div class="location">
                            <i class="fas fa-map-marker-alt"></i>
                            ${safeWeatherInfo.city}${safeWeatherInfo.country ? '（' + safeWeatherInfo.country + '）' : ''}
                        </div>
                        <div class="time">${safeWeatherInfo.local_time}</div>
                    </div>

                    <div class="weather-main-content">
                        <div class="temperature-section">
                            <div class="temperature">
                                ${safeWeatherInfo.temperature}<span class="unit">°C</span>
                            </div>
                            <div class="feels-like">体感 ${safeWeatherInfo.feels_like}°C</div>
                            <div class="weather-description">
                                <i class="fas fa-cloud" id="weather-icon"></i>
                                ${safeWeatherInfo.weather_description}
                            </div>
                        </div>

                        <div class="weather-details">
                            <div class="detail-item" id="humidity-item">
                                <i class="fas fa-tint"></i>
                                <div class="detail-label">湿度</div>
                                <div class="detail-value">${safeWeatherInfo.humidity}%</div>
                            </div>
                            <div class="detail-item" id="wind-item">
                                <i class="fas fa-wind"></i>
                                <div class="detail-label">风速</div>
                                <div class="detail-value">${safeWeatherInfo.wind_speed}km/h</div>
                            </div>
                            <div class="detail-item" id="wind-direction-item">
                                <i class="fas fa-compass"></i>
                                <div class="detail-label">风向</div>
                                <div class="detail-value">${safeWeatherInfo.wind_deg}°</div>
                            </div>
                            <div class="detail-item" id="clouds-item">
                                <i class="fas fa-cloud"></i>
                                <div class="detail-label">云量</div>
                                <div class="detail-value">${safeWeatherInfo.clouds}%</div>
                            </div>
                            <div class="detail-item" id="visibility-item">
                                <i class="fas fa-eye"></i>
                                <div class="detail-label">能见度</div>
                                <div class="detail-value">${safeWeatherInfo.visibility}km</div>
                            </div>
                            <div class="detail-item" id="precipitation-item">
                                <i class="fas fa-tint"></i>
                                <div class="detail-label">降水</div>
                                <div class="detail-value">${safeWeatherInfo.precipitation}mm</div>
                            </div>
                        </div>

                        <div class="weather-visual">
                            <div class="weather-icon">
                                <i class="fas fa-sun" id="main-weather-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            weatherContainer.innerHTML = weatherHTML;

            console.log('天气卡片HTML已插入，开始初始化效果');

            // 标记天气卡片已渲染，防止被覆盖
            weatherContainer.setAttribute('data-weather-rendered', 'true');

            // 初始化天气卡片效果
            setTimeout(() => {
                initializeWeatherCard(safeWeatherInfo);
            }, 100);
        }

        // 天气图标映射
        const weatherIcons = {
            'clear': 'fas fa-sun',
            'sunny': 'fas fa-sun',
            'partly cloudy': 'fas fa-cloud-sun',
            'cloudy': 'fas fa-cloud',
            'overcast': 'fas fa-cloud',
            'rain': 'fas fa-cloud-rain',
            'drizzle': 'fas fa-cloud-drizzle',
            'snow': 'fas fa-snowflake',
            'thunderstorm': 'fas fa-bolt',
            'fog': 'fas fa-smog',
            'mist': 'fas fa-smog',
            'default': 'fas fa-cloud',
            '阴': 'fas fa-cloud',
            '晴': 'fas fa-sun',
            '多云': 'fas fa-cloud-sun',
            '雨': 'fas fa-cloud-rain',
            '雪': 'fas fa-snowflake'
        };

        // 根据天气描述设置图标
        function setWeatherIcon(weatherDescription) {
            const description = weatherDescription.toLowerCase();
            const mainIcon = document.getElementById('main-weather-icon');
            const smallIcon = document.getElementById('weather-icon');

            let iconClass = weatherIcons.default;

            // 优先匹配中文
            if (weatherIcons[weatherDescription]) {
                iconClass = weatherIcons[weatherDescription];
            } else {
                // 匹配英文或包含关键词
                for (const [key, value] of Object.entries(weatherIcons)) {
                    if (description.includes(key)) {
                        iconClass = value;
                        break;
                    }
                }
            }

            if (mainIcon) mainIcon.className = iconClass;
            if (smallIcon) smallIcon.className = iconClass;
        }

        // 根据时间和天气设置动态背景和特效 - 统一优化版本
        function setDynamicBackground(weatherInfo) {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            // 从天气数据中获取当地时间
            const localTimeStr = weatherInfo.local_time;
            let hour;

            try {
                // 解析时间字符串，格式如 "2025-06-20 22:17"
                if (localTimeStr.includes(' ')) {
                    const timePart = localTimeStr.split(' ')[1]; // 获取时间部分 "22:17"
                    hour = parseInt(timePart.split(':')[0]); // 获取小时
                } else {
                    // 如果格式不对，使用浏览器本地时间作为备用
                    hour = new Date().getHours();
                }
            } catch (e) {
                // 解析失败时使用浏览器本地时间
                hour = new Date().getHours();
            }

            const weatherDescription = weatherInfo.weather_description.toLowerCase();
            console.log(`天气特效调试: 当地时间=${localTimeStr}, 小时=${hour}, 天气=${weatherDescription}`);

            let gradient;

            // 清除现有特效
            clearWeatherEffects();

            if (hour >= 6 && hour < 12) {
                // 早晨
                gradient = 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)';
                createSunEffect();
                console.log('应用早晨太阳效果');
            } else if (hour >= 12 && hour < 18) {
                // 下午
                gradient = 'linear-gradient(135deg, #fd79a8 0%, #e84393 100%)';
                createSunEffect();
                console.log('应用下午太阳效果');
            } else if (hour >= 18 && hour < 21) {
                // 傍晚
                gradient = 'linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%)';
                createSunEffect();
                console.log('应用傍晚太阳效果');
            } else {
                // 夜晚
                gradient = 'linear-gradient(135deg, #2d3436 0%, #636e72 100%)';
                createMoonAndStarsEffect();
                console.log('应用夜晚月亮星星效果');
            }

            // 根据天气状况添加特效（优先级：雨雪 > 云朵，但不覆盖时间基础效果）
            if (weatherDescription.includes('雨') || weatherDescription.includes('rain') || weatherDescription.includes('drizzle')) {
                gradient = 'linear-gradient(135deg, #636e72 0%, #2d3436 100%)';
                createRainEffect();
                console.log('应用雨天效果（覆盖时间效果）');
            } else if (weatherDescription.includes('雪') || weatherDescription.includes('snow')) {
                gradient = 'linear-gradient(135deg, #ddd6fe 0%, #8b5cf6 100%)';
                createSnowEffect();
                console.log('应用雪天效果（覆盖时间效果）');
            } else if (weatherDescription.includes('云') || weatherDescription.includes('cloud') || weatherDescription.includes('阴')) {
                // 云朵效果与时间效果叠加，不覆盖背景
                createCloudEffect();
                console.log('应用云朵效果（叠加在时间效果上）');
            }

            weatherCard.style.background = gradient;
        }

        // 移除Edge专用简化背景函数，统一使用动态背景

        // 清除所有天气特效
        function clearWeatherEffects() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            const effects = weatherCard.querySelectorAll('.weather-effect');
            effects.forEach(effect => effect.remove());
        }

        // 创建太阳特效 - 增强版
        function createSunEffect() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            // 创建太阳主体
            const sunRays = document.createElement('div');
            sunRays.className = 'sun-rays weather-effect';
            weatherCard.appendChild(sunRays);

            // 创建粒子系统
            createParticleSystem('sun');
        }

        // 创建粒子系统
        function createParticleSystem(type) {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            const particleSystem = document.createElement('div');
            particleSystem.className = 'particle-system weather-effect';

            const particleCount = type === 'sun' ? 8 : type === 'moon' ? 5 : 6; // 减少粒子数量以优化性能

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle weather-effect';

                // 根据类型设置粒子样式
                if (type === 'sun') {
                    particle.style.background = `rgba(255, ${200 + Math.random() * 55}, 0, ${0.3 + Math.random() * 0.4})`;
                    particle.style.width = particle.style.height = (3 + Math.random() * 4) + 'px';
                } else if (type === 'moon') {
                    particle.style.background = `rgba(255, 255, 255, ${0.2 + Math.random() * 0.3})`;
                    particle.style.width = particle.style.height = (2 + Math.random() * 3) + 'px';
                } else {
                    particle.style.background = `rgba(200, 220, 255, ${0.2 + Math.random() * 0.3})`;
                    particle.style.width = particle.style.height = (2 + Math.random() * 3) + 'px';
                }

                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (6 + Math.random() * 4) + 's';

                particleSystem.appendChild(particle);
            }

            weatherCard.appendChild(particleSystem);
        }

        // 创建月亮和星星特效 - 增强版
        function createMoonAndStarsEffect() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            // 创建月亮
            const moon = document.createElement('div');
            moon.className = 'moon weather-effect';
            weatherCard.appendChild(moon);

            // 创建星星容器
            const starsContainer = document.createElement('div');
            starsContainer.className = 'stars weather-effect';

            // 创建多个星星
            for (let i = 0; i < 20; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 4 + 's';

                // 随机大小的星星
                const size = 2 + Math.random() * 2;
                star.style.width = star.style.height = size + 'px';

                starsContainer.appendChild(star);
            }

            weatherCard.appendChild(starsContainer);

            // 创建月光粒子系统
            createParticleSystem('moon');
        }

        // 创建雨滴特效 - 性能优化版本
        function createRainEffect() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            const rainDropCount = 20; // 减少雨滴数量，从40减到20

            for (let i = 0; i < rainDropCount; i++) {
                const rainDrop = document.createElement('div');
                rainDrop.className = 'rain-drop weather-effect';
                rainDrop.style.left = Math.random() * 100 + '%';
                rainDrop.style.animationDelay = Math.random() * 2.5 + 's';
                rainDrop.style.animationDuration = (Math.random() * 0.6 + 0.9) + 's';

                // 随机雨滴大小
                const width = 2 + Math.random() * 2;
                const height = 15 + Math.random() * 10;
                rainDrop.style.width = width + 'px';
                rainDrop.style.height = height + 'px';

                weatherCard.appendChild(rainDrop);
            }

            // 减少雨滴撞击效果
            createRainSplashEffect();
        }

        // 创建雨滴撞击效果 - 性能优化版本
        function createRainSplashEffect() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            for (let i = 0; i < 4; i++) { // 减少撞击效果数量，从8减到4
                const splash = document.createElement('div');
                splash.className = 'rain-splash weather-effect';
                splash.style.position = 'absolute';
                splash.style.bottom = '10px';
                splash.style.left = Math.random() * 100 + '%';
                splash.style.width = '4px';
                splash.style.height = '4px';
                splash.style.background = 'rgba(135, 206, 235, 0.6)';
                splash.style.borderRadius = '50%';
                splash.style.animation = `splashEffect ${1 + Math.random() * 2}s ease-out infinite`;
                splash.style.animationDelay = Math.random() * 3 + 's';

                weatherCard.appendChild(splash);
            }
        }

        // 创建雪花特效 - 性能优化版本
        function createSnowEffect() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            const snowflakeCount = 15; // 减少雪花数量，从25减到15
            const snowflakeSymbols = ['❄', '❅', '❆', '✻', '✼'];

            for (let i = 0; i < snowflakeCount; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake weather-effect';
                snowflake.textContent = snowflakeSymbols[Math.floor(Math.random() * snowflakeSymbols.length)];
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.animationDelay = Math.random() * 10 + 's';
                snowflake.style.animationDuration = (Math.random() * 5 + 8) + 's';

                // 随机雪花大小和透明度
                const size = 12 + Math.random() * 8;
                snowflake.style.fontSize = size + 'px';
                snowflake.style.opacity = 0.7 + Math.random() * 0.3;

                weatherCard.appendChild(snowflake);
            }

            // 减少雪花粒子系统
            createSnowParticles();
        }

        // 创建雪花粒子系统 - 性能优化版本
        function createSnowParticles() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            for (let i = 0; i < 8; i++) { // 减少粒子数量，从15减到8
                const particle = document.createElement('div');
                particle.className = 'snow-particle weather-effect';
                particle.style.position = 'absolute';
                particle.style.width = (1 + Math.random() * 2) + 'px';
                particle.style.height = (1 + Math.random() * 2) + 'px';
                particle.style.background = 'rgba(255, 255, 255, 0.8)';
                particle.style.borderRadius = '50%';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animation = `snowFall ${8 + Math.random() * 6}s linear infinite`;
                particle.style.animationDelay = Math.random() * 12 + 's';

                weatherCard.appendChild(particle);
            }
        }

        // 创建云朵特效 - 增强版
        function createCloudEffect() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            // 创建三个云朵
            const cloud1 = document.createElement('div');
            cloud1.className = 'cloud cloud-1 weather-effect';
            weatherCard.appendChild(cloud1);

            const cloud2 = document.createElement('div');
            cloud2.className = 'cloud cloud-2 weather-effect';
            weatherCard.appendChild(cloud2);

            const cloud3 = document.createElement('div');
            cloud3.className = 'cloud cloud-3 weather-effect';
            weatherCard.appendChild(cloud3);

            // 创建云朵粒子系统
            createParticleSystem('cloud');
        }

        // 移除Edge浏览器特殊检测，统一优化所有浏览器

        // 主初始化函数 - 统一优化版本
        function initializeWeatherCard(weatherInfo) {
            console.log('天气卡片初始化：使用统一优化模式');

            // 设置天气图标
            setWeatherIcon(weatherInfo.weather_description);

            // 设置动态背景和特效
            setDynamicBackground(weatherInfo);

            // 添加交互效果
            addWeatherCardInteractions();

            // 添加简化的加载动画
            const weatherCard = document.getElementById('weather-card');
            if (weatherCard) {
                // 确保卡片可见
                weatherCard.style.opacity = '1';
                weatherCard.style.visibility = 'visible';

                // 添加轻微的入场动画
                weatherCard.style.transform = 'translateY(10px)';
                weatherCard.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';

                setTimeout(() => {
                    weatherCard.style.transform = 'translateY(0)';
                }, 50);

                // 添加渐进式动画
                setTimeout(() => {
                    const detailItems = weatherCard.querySelectorAll('.detail-item');
                    detailItems.forEach((item, index) => {
                        item.style.transform = 'translateY(10px)';
                        item.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';

                        setTimeout(() => {
                            item.style.transform = 'translateY(0)';
                        }, index * 50);
                    });
                }, 100);
            }
        }

        // 添加天气卡片交互效果 - 统一优化版本
        function addWeatherCardInteractions() {
            const weatherCard = document.getElementById('weather-card');
            if (!weatherCard) return;

            // 使用节流函数减少鼠标移动事件的频率
            let mouseMoveTimeout;

            // 鼠标移动视差效果 - 节流版本
            weatherCard.addEventListener('mousemove', function(e) {
                if (mouseMoveTimeout) return; // 如果上一次还在处理中，跳过

                mouseMoveTimeout = setTimeout(() => {
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;

                    const rotateX = (y - centerY) / centerY * 3; // 减少旋转幅度
                    const rotateY = (centerX - x) / centerX * 3; // 减少旋转幅度

                    this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(5px)`;
                    mouseMoveTimeout = null;
                }, 16); // 约60fps，减少计算频率
            });

            // 鼠标离开重置
            weatherCard.addEventListener('mouseleave', function() {
                if (mouseMoveTimeout) {
                    clearTimeout(mouseMoveTimeout);
                    mouseMoveTimeout = null;
                }
                this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
            });

            // 点击波纹效果
            weatherCard.addEventListener('click', function(e) {
                const ripple = document.createElement('div');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: rippleEffect 0.6s ease-out;
                    pointer-events: none;
                    z-index: 100;
                `;

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        }

        // 学习资源入口配置
        const entranceConfigs = [
            {
                name: 'viewBlog',
                gradient: ['#667eea', '#764ba2'],
                color: '#fff',
                icon: '<i class="fas fa-book-open"></i><i class="fas fa-bookmark" style="position: absolute; top: -5px; right: -5px; font-size: 0.6em; color: #FFD700;"></i>',
                text: 'Blog笔记库',
                description: '📚 浏览学习笔记与知识总结',
                path: '/api/blog_list/'
            },
            {
                name: 'courseVideo',
                gradient: ['#f093fb', '#f5576c'],
                color: '#fff',
                icon: '<i class="fas fa-play-circle"></i><i class="fas fa-graduation-cap" style="position: absolute; top: -8px; right: -8px; font-size: 0.7em; color: #FFE4B5;"></i>',
                text: '视频课程',
                description: '🎬 观看精品教学视频内容',
                path: '/api/collections_page'
            },
            {
                name: 'writeBlog',
                gradient: ['#4facfe', '#00f2fe'],
                color: '#fff',
                icon: '<i class="fas fa-feather-alt"></i><i class="fas fa-sparkles" style="position: absolute; top: -6px; right: -6px; font-size: 0.5em; color: #FFD700; animation: sparkle 2s ease-in-out infinite;"></i>',
                text: '创作中心',
                description: '✍️ 编写与分享学习心得',
                path: '/blog/'
            },
            {
                name: 'aiNote',
                gradient: ['#fa709a', '#fee140'],
                color: '#fff',
                icon: '<i class="fas fa-robot"></i><i class="fas fa-brain" style="position: absolute; top: -7px; right: -7px; font-size: 0.6em; color: #E6E6FA; animation: pulse 1.5s ease-in-out infinite;"></i>',
                text: 'AI 学习助手',
                description: '🤖 智能笔记整理与学习辅导',
                path: '/api/mermindfig/chat_log_web/chat_viewer.html'
            }
        ];

        // 加载入口链接
        async function loadEntranceLinks() {
            const entranceGrid = document.getElementById('entrance-grid');

            try {
                const response = await fetch(`/api/get-entrance-data?t=${Date.now()}`);
                const result = await response.json();

                if (result.success) {
                    const { username, password, token, origin, is_admin, is_vip } = result.data;

                    // 加载主要入口
                    entranceGrid.innerHTML = '';
                    entranceConfigs.forEach(config => {
                        const url = `${origin}${config.path}?user=${username}&key=${password}&token=${token}`;

                        const entranceButton = document.createElement('a');
                        entranceButton.href = url;
                        entranceButton.target = '_blank';
                        entranceButton.className = `entrance-btn entrance-${config.name}`;
                        entranceButton.innerHTML = `
                            <div class="entrance-icon">${config.icon}</div>
                            <div class="entrance-content">
                                <div class="entrance-text">${config.text}</div>
                                <div class="entrance-description">${config.description}</div>
                            </div>
                        `;

                        // 设置渐变背景
                        entranceButton.style.background = `linear-gradient(135deg, ${config.gradient[0]}, ${config.gradient[1]})`;
                        entranceButton.style.color = config.color;

                        entranceGrid.appendChild(entranceButton);
                    });

                    // 处理API密钥申请链接和输入框的显示
                    const apiLinksContainer = document.getElementById('api-links-container');
                    const apiKeyInputWrapper = document.querySelector('.api-key-input .input-wrapper');
                    
                    if (is_admin || is_vip) {
                        // 隐藏API密钥申请链接和输入框
                        if (apiLinksContainer) {
                            apiLinksContainer.style.display = 'none';
                        }
                        if (apiKeyInputWrapper) {
                            apiKeyInputWrapper.style.display = 'none';
                        }
                    } else {
                        // 显示API密钥申请链接和输入框
                        if (apiLinksContainer) {
                            apiLinksContainer.style.display = 'block';
                        }
                        if (apiKeyInputWrapper) {
                            apiKeyInputWrapper.style.display = 'flex';
                        }
                    }

                    // 加载管理员侧边栏链接（如果是管理员）
                    if (is_admin) {
                        loadAdminSidebarLinks(username, password, token, origin);
                    }

                    // 控制历史反馈按钮的显示（只有admin用户可见）
                    const feedbackHistorySection = document.getElementById('feedback-history-section');
                    if (feedbackHistorySection) {
                        if (is_admin) {
                            feedbackHistorySection.style.display = 'block';
                        } else {
                            feedbackHistorySection.style.display = 'none';
                        }
                    }
                } else {
                    entranceGrid.innerHTML = '<div class="entrance-error">无法加载用户数据</div>';
                }
            } catch (error) {
                console.error('Error loading entrance data:', error);
                entranceGrid.innerHTML = '<div class="entrance-error">加载失败，请刷新页面重试</div>';
            }
        }

        // 加载管理员侧边栏链接
        function loadAdminSidebarLinks(username, password, token, origin) {
            const adminSection = document.getElementById('admin-section');
            const adminDivider = document.getElementById('admin-divider');
            const adminPageLink = document.getElementById('admin-page-link');
            const adminClassesLink = document.getElementById('admin-classes-link');
            const adminDashboardLink = document.getElementById('admin-dashboard-link');

            // 构建管理员链接
            const adminUrl = `${origin}/api/blog_list_root/?user=${username}&key=${password}&token=${token}`;
            const adminDashboardUrl = `${origin}/api/admin_dashboard/?user=${username}&key=${password}&token=${token}`;
            const adminClassesUrl = `${origin}/api/class_user_data/class_user_data.html?user=${username}&key=${password}&token=${token}`;

            // 设置链接
            if (adminPageLink) adminPageLink.href = adminUrl;
            if (adminClassesLink) adminClassesLink.href = adminClassesUrl;
            if (adminDashboardLink) adminDashboardLink.href = adminDashboardUrl;

            // 显示管理员区域
            if (adminSection) adminSection.style.display = 'block';
            if (adminDivider) adminDivider.style.display = 'block';
        }

        // 加载AI工具
        async function loadAITools() {
            console.log('开始加载AI工具...');
            const aiToolsContainer = document.getElementById('ai-tools-container');

            if (!aiToolsContainer) {
                console.error('AI工具容器未找到');
                return;
            }

            // 先显示加载状态
            aiToolsContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">正在加载AI工具...</div>';

            try {
                console.log('获取用户数据...');
                const response = await fetch(`/api/get-entrance-data?t=${Date.now()}`);
                const result = await response.json();
                console.log('用户数据获取结果:', result);

                if (result.success) {
                    const { username, password, token, origin } = result.data;

                    // 构建AI工具URL
                    const aiToolsData = [
                        {
                            name: 'ai_demo',
                            url: `${origin}/api/ai-demo?user=${username}&key=${password}&token=${token}`,
                            icon: '✂️🖼️',
                            title: 'AI 去背景',
                            description: '智能去背景，生成证件照',
                            tag: '✨实用工具',
                            tagClass: 'free'
                        },
                        {
                            name: 'electric_demo',
                            url: `${origin}/api/mermindfig/electric.html?user=${username}&key=${password}&token=${token}`,
                            icon: '⚡🧲',
                            title: '电磁场演示',
                            description: '可视化电磁场效应',
                            tag: '✨学科工具',
                            tagClass: 'free'
                        },
                        {
                            name: 'latex_editor',
                            url: `${origin}/api/mermindfig/latexeditor.html?user=${username}&key=${password}&token=${token}`,
                            icon: '📝📐',
                            title: 'LaTeX 编辑器',
                            description: '在线LaTeX公式编辑',
                            tag: '✨学科工具',
                            tagClass: 'free'
                        },
                        {
                            name: 'markdown_renderer',
                            url: `${origin}/api/mermindfig/markdown-renderer.html?user=${username}&key=${password}&token=${token}`,
                            icon: '📄✨',
                            title: 'Markdown 渲染器',
                            description: '实时Markdown预览',
                            tag: '✨实用工具',
                            tagClass: 'free'
                        },
                        {
                            name: 'circuit',
                            url: `${origin}/api/circuit?user=${username}&key=${password}&token=${token}`,
                            icon: '🔌⚡',
                            title: '电路仿真',
                            description: '电路仿真，体验电路',
                            tag: '✨学科工具',
                            tagClass: 'free'
                        },
                        {
                            name: 'paint_board',
                            url: `${origin}/api/paint-board?user=${username}&key=${password}&token=${token}`,
                            icon: '🎨💡',
                            title: '创意画板',
                            description: '灵感涌现，自由创作',
                            tag: '✨实用工具',
                            tagClass: 'free'
                        },
                        {
                            name: 'florence',
                            url: `${origin}/api/florence?user=${username}&key=${password}&token=${token}`,
                            icon: '🖼️🔍',
                            title: '图片识别',
                            description: '智能识别，体验AI',
                            tag: '✨AI体验',
                            tagClass: 'free'
                        },
                        {
                            name: 'onnxdemo',
                            url: `${origin}/api/onnxdemo?user=${username}&key=${password}&token=${token}`,
                            icon: '🤖👁️',
                            title: 'ONNX视觉模型',
                            description: 'AI眼中的世界',
                            tag: '✨AI体验',
                            tagClass: 'free'
                        }
                    ];

                    // 获取工具访问次数
                    console.log('获取工具访问次数...');
                    const toolVisitsResponse = await fetch(`/api/get-tool-visits?t=${Date.now()}`);
                    let toolVisits = {};
                    console.log('工具访问次数响应状态:', toolVisitsResponse.status);
                    if (toolVisitsResponse.ok) {
                        const toolVisitsResult = await toolVisitsResponse.json();
                        console.log('工具访问次数结果:', toolVisitsResult);
                        if (toolVisitsResult.success) {
                            toolVisits = toolVisitsResult.tool_visits;
                        }
                    } else {
                        console.warn('获取工具访问次数失败，使用默认值');
                    }

                    // 生成AI工具HTML
                    console.log('开始生成AI工具HTML，工具数量:', aiToolsData.length);
                    aiToolsContainer.innerHTML = '';
                    aiToolsData.forEach((tool, index) => {
                        console.log(`生成工具 ${index + 1}:`, tool.title);
                        const toolCard = document.createElement('a');
                        toolCard.href = tool.url;
                        toolCard.target = '_blank';
                        toolCard.className = 'ai-tool-card';
                        toolCard.style.textDecoration = 'none';

                        const visitCount = toolVisits[tool.name] || 0;

                        toolCard.innerHTML = `
                            <span class="ai-tool-tag ${tool.tagClass}">${tool.tag}</span>
                            <div class="ai-tool-icon">${tool.icon}</div>
                            <div class="ai-tool-title">${tool.title}</div>
                            <p class="ai-tool-description">${tool.description}</p>
                            <div class="ai-usage-visit-count">
                                <span>👀</span>
                                <span>${visitCount} 次访问</span>
                            </div>
                        `;

                        aiToolsContainer.appendChild(toolCard);
                    });

                    // 添加即将推出的工具
                    const comingSoonCard = document.createElement('div');
                    comingSoonCard.className = 'ai-tool-card coming-soon';
                    comingSoonCard.innerHTML = `
                        <span class="ai-tool-tag coming-soon">🛠️开发中</span>
                        <div class="ai-tool-icon">🎨🖌️🤖</div>
                        <div class="ai-tool-title">AI 绘画</div>
                        <p class="ai-tool-description">即将推出 - 智能AI绘画工具</p>
                    `;
                    aiToolsContainer.appendChild(comingSoonCard);

                } else {
                    aiToolsContainer.innerHTML = '<div class="ai-tools-error">无法加载AI工具数据</div>';
                }
            } catch (error) {
                console.error('Error loading AI tools:', error);
                aiToolsContainer.innerHTML = '<div class="ai-tools-error">加载失败，请刷新页面重试</div>';
            }
        }

        // 简化的骨架屏显示 - 移除所有DOM监控以提升性能
        (function() {
            // 简单显示骨架屏，不进行内容检查
            function showSkeleton() {
                const weatherContainer = document.getElementById('weather-info');
                const skeleton = document.getElementById('weather-skeleton');

                if (weatherContainer && skeleton) {
                    skeleton.style.display = 'block';
                    skeleton.style.visibility = 'visible';
                    skeleton.style.opacity = '1';
                    console.log('骨架屏已显示');
                } else if (weatherContainer && !skeleton) {
                    // 如果没有骨架屏，创建一个
                    showWeatherSkeleton();
                } else {
                    // 如果元素还没有加载，稍后重试
                    setTimeout(showSkeleton, 10);
                }
            }

            // 立即尝试显示，如果DOM还没准备好就等待
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', showSkeleton);
            } else {
                showSkeleton();
            }

            console.log('✅ 已移除所有DOM监控以优化性能');
        })();

        // 天气容器保护机制已禁用以提升性能
        function protectWeatherContainer() {
            console.log('天气容器保护机制已禁用，以优化浏览器性能');
            // MutationObserver监控已移除，减少DOM监控开销
            // 如果遇到天气卡片显示问题，可以刷新页面重新加载
        }

        // 显示天气骨架屏
        function showWeatherSkeleton() {
            const weatherContainer = document.getElementById('weather-info');
            if (!weatherContainer) return;

            const skeletonHTML = `
                <div class="weather-skeleton" id="weather-skeleton">
                    <div class="weather-card skeleton-card">
                        <div class="weather-header">
                            <div class="location skeleton-text skeleton-location"></div>
                            <div class="time skeleton-text skeleton-time"></div>
                        </div>
                        <div class="weather-main-content">
                            <div class="temperature-section">
                                <div class="temperature skeleton-text skeleton-temp"></div>
                                <div class="feels-like skeleton-text skeleton-feels"></div>
                                <div class="weather-description skeleton-text skeleton-desc"></div>
                            </div>
                            <div class="weather-details">
                                <div class="detail-item">
                                    <div class="detail-label skeleton-text skeleton-label"></div>
                                    <div class="detail-value skeleton-text skeleton-value"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label skeleton-text skeleton-label"></div>
                                    <div class="detail-value skeleton-text skeleton-value"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label skeleton-text skeleton-label"></div>
                                    <div class="detail-value skeleton-text skeleton-value"></div>
                                </div>
                            </div>
                            <div class="weather-visual">
                                <div class="weather-icon skeleton-icon"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            weatherContainer.innerHTML = skeletonHTML;
            console.log('骨架屏已重新显示');
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 性能优化提示
            console.log('🚀 天气卡片统一性能优化已启用：');
            console.log('  ✅ 完全移除DOM监控 - 大幅提升性能');
            console.log('  ✅ 移除MutationObserver - 减少内存占用');
            console.log('  ✅ 取消定期检查 - 降低CPU使用');
            console.log('  ✅ 移除已弃用的CSS媒体查询');
            console.log('  ✅ 统一优化所有浏览器 - 一致体验');
            console.log('  ✅ 保持完整特效 - 优化性能');

            // 确保骨架屏显示
            const skeleton = document.getElementById('weather-skeleton');
            if (skeleton) {
                skeleton.style.display = 'block';
                console.log('DOMContentLoaded: 骨架屏已显示');
            }

            // 启动天气容器保护机制
            protectWeatherContainer();

            // 立即开始加载天气信息，不等待其他内容
            loadWeather();

            // 确保所有元素都已加载
            setTimeout(() => {
                loadEntranceLinks();
                loadAITools(); // 加载AI工具
                loadTotalVisits(); // 加载总访问次数

                // 初始化API密钥显示 - 确保select元素已经渲染
                const apiKeyTypeSelect = document.getElementById('api-key-type');
                if (apiKeyTypeSelect) {
                    // 从后端获取用户的默认API密钥类型
                    initializeApiKeyType();
                } else {
                    console.error('API密钥类型选择框未找到');
                }

                // 获取用户状态
                const htmlElement = document.documentElement;
                const isVip = htmlElement.dataset.isVip === 'true';
                const isAdmin = htmlElement.dataset.isAdmin === 'true';

                // 获取API链接容器
                const apiLinksContainer = document.getElementById('api-links-container');

                // 如果是VIP或管理员，隐藏API链接
                if (isVip || isAdmin) {
                    if (apiLinksContainer) {
                        apiLinksContainer.style.display = 'none';
                    }
                }
            }, 50); // 减少延迟时间
        });

        // 加载总访问次数
        async function loadTotalVisits() {
            try {
                const response = await fetch(`/api/total-visits?t=${Date.now()}`);
                const result = await response.json();

                if (result.success) {
                    document.getElementById('total-visits-count').textContent = result.total_visits;
                } else {
                    document.getElementById('total-visits-count').textContent = '0';
                }
            } catch (error) {
                console.error('加载总访问次数失败:', error);
                document.getElementById('total-visits-count').textContent = '0';
            }
        }

        // 显示访问记录
        async function showAccessLog() {
            try {
                const response = await fetch(`/api/access-log?t=${Date.now()}`);
                const result = await response.json();

                if (result.success) {
                    displayAccessLog(result.data);
                    document.getElementById('access-log-modal').style.display = 'block';
                } else {
                    showMessage(result.message || '获取访问记录失败', 'error');
                }
            } catch (error) {
                console.error('获取访问记录失败:', error);
                showMessage('获取访问记录失败，请重试', 'error');
            }
        }

        // 关闭访问记录模态框
        function closeAccessLog() {
            document.getElementById('access-log-modal').style.display = 'none';
        }

        // 显示访问记录数据
        function displayAccessLog(data) {
            // 更新统计卡片
            const statsContainer = document.getElementById('access-log-stats');
            statsContainer.innerHTML = `
                <div class="access-stat-card">
                    <div class="access-stat-number">${data.total_visits}</div>
                    <div class="access-stat-label">总访问次数</div>
                </div>
                <div class="access-stat-card">
                    <div class="access-stat-number">${data.user_visits.length}</div>
                    <div class="access-stat-label">访问用户数</div>
                </div>
                <div class="access-stat-card">
                    <div class="access-stat-number">${data.user_visits.filter(u => u.username !== 'anonymous').length}</div>
                    <div class="access-stat-label">注册用户数</div>
                </div>
                <div class="access-stat-card">
                    <div class="access-stat-number">${data.user_visits.find(u => u.username === 'anonymous')?.count || 0}</div>
                    <div class="access-stat-label">匿名访问次数</div>
                </div>
            `;

            // 更新表格
            const tableBody = document.getElementById('access-log-table-body');
            tableBody.innerHTML = '';

            data.user_visits.forEach(user => {
                const row = document.createElement('tr');

                // 用户名徽章
                let userBadge = '';
                if (user.username === 'admin') {
                    userBadge = '<span class="user-badge admin">管理员</span>';
                } else if (user.username === 'anonymous') {
                    userBadge = '<span class="user-badge anonymous">匿名用户</span>';
                } else {
                    userBadge = `<span class="user-badge user">${user.username}</span>`;
                }

                // 格式化时间
                const firstVisit = new Date(user.first_visit).toLocaleString('zh-CN');
                const lastVisit = new Date(user.last_visit).toLocaleString('zh-CN');

                row.innerHTML = `
                    <td>${userBadge}</td>
                    <td><strong>${user.count}</strong></td>
                    <td>${firstVisit}</td>
                    <td>${lastVisit}</td>
                `;

                tableBody.appendChild(row);
            });
        }

        // 点击模态框背景关闭
        document.getElementById('access-log-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAccessLog();
            }
        });

        document.getElementById('toggle-password').addEventListener('click', function() {
            const input = document.getElementById('api-key-input');
            if (input.type === 'password') {
                input.type = 'text';
                this.querySelector('.eye-icon').style.opacity = '0.8';
            } else {
                input.type = 'password';
                this.querySelector('.eye-icon').style.opacity = '0.4';
            }
        });

        // 用户反馈功能
        document.getElementById('submit-feedback').addEventListener('click', async function() {
            const feedbackInput = document.getElementById('feedback-input');
            const feedback = feedbackInput.value.trim();
            const submitButton = this;

            if (!feedback) {
                showMessage('请输入反馈内容', 'error');
                return;
            }

            // 禁用提交按钮，防止重复提交
            submitButton.disabled = true;
            submitButton.textContent = '提交中...';

            try {
                const response = await fetch('/api/submit-feedback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        feedback: feedback
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('反馈提交成功，感谢您的意见！', 'success');
                    feedbackInput.value = ''; // 清空输入框
                } else {
                    showMessage(result.message || '反馈提交失败', 'error');
                }
            } catch (error) {
                console.error('提交反馈错误:', error);
                showMessage('反馈提交失败，请重试', 'error');
            } finally {
                // 恢复提交按钮状态
                submitButton.disabled = false;
                submitButton.textContent = '提交反馈';
            }
        });

        // 查看历史反馈
        document.getElementById('view-feedback-history').addEventListener('click', async function() {
            try {
                const response = await fetch(`/api/get-feedback-history?t=${Date.now()}`);
                const result = await response.json();

                if (result.success) {
                    displayFeedbackHistory(result.feedback_list);
                } else {
                    showMessage(result.message || '获取反馈历史失败', 'error');
                }
            } catch (error) {
                console.error('获取反馈历史错误:', error);
                showMessage('获取反馈历史失败，请重试', 'error');
            }
        });

        // 显示反馈历史的弹窗
        function displayFeedbackHistory(feedbackList) {
            if (!feedbackList || feedbackList.length === 0) {
                showMessage('暂无反馈历史', 'info');
                return;
            }

            // 创建弹窗
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 8px;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
            `;

            // 创建标题和关闭按钮
            const header = document.createElement('div');
            header.style.cssText = `
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
            `;

            const title = document.createElement('h3');
            title.textContent = '反馈历史';
            title.style.margin = '0';

            const closeBtn = document.createElement('button');
            closeBtn.textContent = '×';
            closeBtn.style.cssText = `
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
            `;
            closeBtn.onclick = () => document.body.removeChild(modal);

            header.appendChild(title);
            header.appendChild(closeBtn);

            // 创建反馈列表
            const feedbackContainer = document.createElement('div');
            feedbackList.forEach(item => {
                const feedbackItem = document.createElement('div');
                feedbackItem.style.cssText = `
                    border: 1px solid #eee;
                    border-radius: 4px;
                    padding: 15px;
                    margin-bottom: 10px;
                    background-color: #f9f9f9;
                `;

                feedbackItem.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-weight: bold; color: #333;">反馈 #${item.index}</span>
                        <span style="color: #666; font-size: 12px;">${item.timestamp}</span>
                    </div>
                    <div style="color: #555; line-height: 1.4;">${item.feedback}</div>
                `;

                feedbackContainer.appendChild(feedbackItem);
            });

            modalContent.appendChild(header);
            modalContent.appendChild(feedbackContainer);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 点击背景关闭弹窗
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }

    </script>
</body>
</html>