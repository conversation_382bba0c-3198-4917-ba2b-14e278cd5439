<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LaTeX 公式渲染测试</title>
    
    <!-- KaTeX for math formulas -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-case {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .original {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .processed {
            background: #d1ecf1;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .rendered {
            background: #d4edda;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        /* KaTeX数学公式样式 */
        .katex-display {
            overflow-x: auto;
            overflow-y: hidden;
            padding: 1.2em 0;
            margin: 1.5em 0;
            text-align: center;
            background: rgba(248, 249, 250, 0.5);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .katex-block-wrapper {
            margin: 1.8em 0;
            text-align: center;
            clear: both;
        }

        .katex {
            font-size: 1.15em;
            line-height: 1.4;
        }

        /* 行内公式样式 */
        .katex-inline {
            font-size: 1.05em;
            vertical-align: baseline;
        }

        /* 行间公式特殊样式 */
        .katex-display .katex {
            font-size: 1.25em;
            display: block;
            margin: 0 auto;
        }

        .katex-error {
            color: #cc0000;
            background: #fff2f2;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #ffcccc;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>LaTeX 公式渲染测试</h1>
    
    <div class="test-case">
        <h3>测试用例 1: 用户提供的示例</h3>
        <div class="original">
            原始内容：<br>
            ### 详细解析\n\n本题涉及一个边长为 \\(a\\) 的正方形平面，\n$$\na^2+\\frac{a}{2}\n$$\n, 在其垂直轴线上距离中心点 \\(O\\) 为 \\(a/2\\) 处放置一个点电荷 \\(q\\)（正电荷）
        </div>
        <div class="processed" id="processed1"></div>
        <div class="rendered" id="rendered1"></div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 2: HTML 标签处理</h3>
        <div class="original">
            原始内容：<br>
            点电荷通量与立体角关系：对于一个点电荷，通过任意开放曲面的通量取决于该曲面对点电荷所张的立体角：&lt;br&gt;$&lt;br&gt;\\Phi_E = \\frac{q}{4\\pi\\epsilon_0} \\cdot \\Omega&lt;br&gt;$&lt;br&gt;其中 \\Omega 是立体角
        </div>
        <div class="processed" id="processed2"></div>
        <div class="rendered" id="rendered2"></div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 3: \\[ \\] 格式</h3>
        <div class="original">
            原始内容：<br>
            通过该平面的电场强度通量为 \\(\\frac{q}{6\\epsilon_0}\\)，选项 A 正确。\n\n\\[\n\\Phi_{\\text{total}} = 6 \\Phi_{\\text{face}}\n\\]\n\n代入总通量公式：
        </div>
        <div class="processed" id="processed3"></div>
        <div class="rendered" id="rendered3"></div>
    </div>

    <!-- Core libraries for markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.js"></script>

    <script>
        // 复制 gen_questions.html 中的预处理函数
        function preprocessContent(content) {
            // 首先处理HTML标签转换为纯文本格式
            // 将 <br> 标签转换为换行符
            content = content.replace(/<br\s*\/?>/gi, '\n');

            // 将其他HTML标签去除（保留内容）
            content = content.replace(/<[^>]*>/g, '');

            // 处理HTML实体
            content = content.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');

            // 处理换行符转换
            content = content.replace(/\\n/g, '\n');

            // 步骤1: 将 \\( 和 \\) 格式转换为 $ 格式（行内公式）
            content = content.replace(/\\\\\(/g, '$');
            content = content.replace(/\\\\\)/g, '$');

            // 步骤2: 将 \( 和 \) 格式转换为 $ 格式（行内公式）
            content = content.replace(/\\\(/g, '$');
            content = content.replace(/\\\)/g, '$');

            // 步骤3: 将 \\[ 和 \\] 格式转换为 $$ 格式（行间公式）
            content = content.replace(/\\\\\[/g, '$$');
            content = content.replace(/\\\\\]/g, '$$');

            // 步骤4: 将 \[ 和 \] 格式转换为 $$ 格式（行间公式）
            content = content.replace(/\\\[/g, '$$');
            content = content.replace(/\\\]/g, '$$');

            // 步骤5: 处理特殊情况 - 单独的 $ 符号被换行符包围的情况，转换为行间公式
            // 匹配模式：$\n...内容...\n$ 转换为 $$\n...内容...\n$$
            content = content.replace(/\$\s*\n([\s\S]*?)\n\s*\$/g, '$$\n$1\n$$');

            // 步骤6: 确保行间公式 $$ 格式有正确的换行和空间
            content = content.replace(/\$\$\s*\n([\s\S]*?)\n\s*\$\$/g, function(match, formula) {
                // 保持公式内部的换行，但确保公式前后有适当的空间
                return `$$\n${formula.trim()}\n$$`;
            });

            // 步骤7: 处理连续的 $$ 符号（避免错误的嵌套）
            content = content.replace(/\$\$\$\$/g, '$$');

            return content;
        }

        // 初始化 markdown 渲染器（简化版）
        let markdownRenderer = null;
        
        function initMarkdownRenderer() {
            markdownRenderer = markdownit({
                html: true,
                linkify: true,
                typographer: true,
                breaks: true
            });

            // 添加 KaTeX 支持（简化版）
            markdownRenderer.use(function(md) {
                // 行内数学公式
                function mathInline(state, silent) {
                    if (state.src[state.pos] !== '$') return false;
                    if (state.src[state.pos + 1] === '$') return false; // 跳过块级公式
                    
                    let pos = state.pos + 1;
                    let found = false;
                    
                    while (pos < state.posMax) {
                        if (state.src[pos] === '$') {
                            found = true;
                            break;
                        }
                        if (state.src[pos] === '\n') break;
                        pos++;
                    }
                    
                    if (!found) return false;
                    
                    const content = state.src.slice(state.pos + 1, pos);
                    if (!silent) {
                        const token = state.push('math_inline', 'math', 0);
                        token.content = content;
                    }
                    
                    state.pos = pos + 1;
                    return true;
                }
                
                // 块级数学公式
                function mathBlock(state, startLine, endLine, silent) {
                    let pos = state.bMarks[startLine] + state.tShift[startLine];
                    let max = state.eMarks[startLine];
                    
                    if (state.src.slice(pos, pos + 2) !== '$$') return false;
                    
                    pos += 2;
                    let nextLine = startLine;
                    let found = false;
                    let content = '';
                    
                    // 检查单行公式
                    const restOfLine = state.src.slice(pos, max);
                    const singleLineMatch = restOfLine.match(/^(.*?)\$\$/);
                    if (singleLineMatch) {
                        content = singleLineMatch[1];
                        found = true;
                    } else {
                        // 多行公式
                        const afterDelim = state.src.slice(pos, max).trim();
                        if (afterDelim === '') {
                            for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {
                                let lineStart = state.bMarks[nextLine] + state.tShift[nextLine];
                                let lineEnd = state.eMarks[nextLine];
                                let lineContent = state.src.slice(lineStart, lineEnd);
                                
                                const closingMatch = lineContent.match(/^(.*?)\$\$/);
                                if (closingMatch) {
                                    content += (content ? '\n' : '') + closingMatch[1];
                                    found = true;
                                    break;
                                } else {
                                    content += (content ? '\n' : '') + lineContent;
                                }
                            }
                        }
                    }
                    
                    if (!found) return false;
                    
                    state.line = nextLine + 1;
                    const token = state.push('math_block', 'math', 0);
                    token.block = true;
                    token.content = content.trim();
                    
                    return true;
                }
                
                md.inline.ruler.after('escape', 'math_inline', mathInline);
                md.block.ruler.before('fence', 'math_block', mathBlock);
                
                // 渲染规则
                md.renderer.rules.math_inline = function(tokens, idx) {
                    try {
                        return katex.renderToString(tokens[idx].content, {
                            displayMode: false,
                            throwOnError: false
                        });
                    } catch (err) {
                        return `<span class="katex-error">${tokens[idx].content}</span>`;
                    }
                };
                
                md.renderer.rules.math_block = function(tokens, idx) {
                    try {
                        const renderedMath = katex.renderToString(tokens[idx].content, {
                            displayMode: true,
                            throwOnError: false
                        });
                        
                        return `<div class="katex-block-wrapper">
                            <div class="katex-display">
                                ${renderedMath}
                            </div>
                        </div>`;
                    } catch (err) {
                        return `<div class="katex-block-wrapper">
                            <div class="katex-display katex-error">
                                $$${tokens[idx].content}$$
                            </div>
                        </div>`;
                    }
                };
            });
        }

        function renderMarkdown(content) {
            if (!markdownRenderer) return content;
            
            try {
                const processedContent = preprocessContent(content);
                return markdownRenderer.render(processedContent);
            } catch (error) {
                return `<div class="error">渲染错误: ${error.message}</div>`;
            }
        }

        // 测试用例
        const testCases = [
            "### 详细解析\\n\\n本题涉及一个边长为 \\\\(a\\\\) 的正方形平面，\\n$$\\na^2+\\\\frac{a}{2}\\n$$\\n, 在其垂直轴线上距离中心点 \\\\(O\\\\) 为 \\\\(a/2\\\\) 处放置一个点电荷 \\\\(q\\\\)（正电荷）",
            "点电荷通量与立体角关系：对于一个点电荷，通过任意开放曲面的通量取决于该曲面对点电荷所张的立体角：<br>$<br>\\\\Phi_E = \\\\frac{q}{4\\\\pi\\\\epsilon_0} \\\\cdot \\\\Omega<br>$<br>其中 \\\\Omega 是立体角",
            "通过该平面的电场强度通量为 \\\\(\\\\frac{q}{6\\\\epsilon_0}\\\\)，选项 A 正确。\\n\\n\\\\[\\n\\\\Phi_{\\\\text{total}} = 6 \\\\Phi_{\\\\text{face}}\\n\\\\]\\n\\n代入总通量公式："
        ];

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', function() {
            initMarkdownRenderer();
            
            testCases.forEach((testCase, index) => {
                const processed = preprocessContent(testCase);
                const rendered = renderMarkdown(testCase);
                
                document.getElementById(`processed${index + 1}`).innerHTML = 
                    `预处理后：<br><code>${processed.replace(/\n/g, '\\n')}</code>`;
                document.getElementById(`rendered${index + 1}`).innerHTML = rendered;
            });
        });
    </script>
</body>
</html>
